import 'package:flutter/material.dart';

class HighlightedText extends StatelessWidget {
  final String text;
  final String? keyword;
  final Color highlightColor;
  final TextStyle? textStyle;
  final TextStyle? highlightStyle;
  final int? maxLine;

  const HighlightedText({
    super.key,
    required this.text,
    this.keyword,
    this.highlightColor = Colors.blue,
    this.maxLine,
    this.textStyle,
    this.highlightStyle,
  });

  @override
  Widget build(BuildContext context) {
    if (keyword == null || keyword!.isEmpty || !text.contains(keyword!)) {
      return Text(text, style: textStyle);
    }

    List<TextSpan> spans = [];
    int start = 0;
    while (start < text.length) {
      int index = text.indexOf(keyword!, start);
      if (index == -1) {
        spans.add(TextSpan(text: text.substring(start), style: textStyle));
        break;
      }

      if (index > start) {
        spans.add(
            TextSpan(text: text.substring(start, index), style: textStyle));
      }

      spans.add(TextSpan(
        text: keyword!,
        style: highlightStyle ??
            TextStyle(
              color: highlightColor,
              fontWeight: FontWeight.bold,
            ),
      ));

      start = index + keyword!.length;
    }

    return RichText(
      textScaler: MediaQuery.of(context).textScaler,
      maxLines: maxLine,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: spans,
        style: textStyle ?? const TextStyle(color: Colors.black),
      ),
    );
  }
}
