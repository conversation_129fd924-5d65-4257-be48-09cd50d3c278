import 'package:and/common/res/colours.dart';
import 'package:flutter/material.dart';

class ReminderAnimationWidget extends StatefulWidget {
  final int count;
  final bool white;

  ReminderAnimationWidget({required this.count, required this.white});

  @override
  _ReminderAnimationWidgetState createState() => _ReminderAnimationWidgetState();
}

class _ReminderAnimationWidgetState extends State<ReminderAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _colorAnimation;
  late Color themeColor;
  late Color originalColor;
  int _currentCount = 0; // 记录当前动画执行次数

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_currentCount < widget.count - 1) {
          _controller.reverse(); // 反向动画
          _currentCount++;
        } else {
          Future.delayed(Duration(milliseconds: 100), () {
            if (mounted) {
              setState(() {
                // 恢复到原始颜色
                _colorAnimation = ColorTween(
                  begin: themeColor,
                  end: originalColor,
                ).animate(_controller);
                _controller.forward();
              });
            }
          });
        }
      } else if (status == AnimationStatus.dismissed) {
        _controller.forward();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    themeColor = DColor.primaryColor;
    originalColor = widget.white ? Colors.white : Colors.transparent;

    _colorAnimation = ColorTween(
      begin: originalColor,
      end: themeColor,
    ).animate(_controller);

    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _colorAnimation,
      builder: (context, child) {
        return Container(
          height: 50,
          color: _colorAnimation.value,
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}