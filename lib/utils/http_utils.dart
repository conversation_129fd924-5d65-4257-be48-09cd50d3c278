import 'dart:convert';
import 'dart:io';

import 'package:and/app.dart';
import 'package:and/bloc/navigation/navi_count_bloc.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/eventbus/conversation_sync_event.dart';
import 'package:and/http/http_config.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/channel.dart';
import 'package:and/io/common.dart';
import 'package:and/io/conversation.dart';
import 'package:and/io/file.dart';
import 'package:and/io/friend.dart';
import 'package:and/io/group.dart';
import 'package:and/io/message.dart';
import 'package:and/io/share.dart';
import 'package:and/io/user.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/app_version.dart';
import 'package:and/model/call_response.dart';
import 'package:and/model/channel_info.dart';
import 'package:and/model/device.dart';
import 'package:and/model/extension/channel_info_ext.dart';
import 'package:and/model/extension/channel_msg_sync_msg_ext.dart';
import 'package:and/model/extension/conversation_extra_sync_ext.dart';
import 'package:and/model/extension/conversation_sync_ext.dart';
import 'package:and/model/extension/group_ext.dart';
import 'package:and/model/extension/group_member_ext.dart';
import 'package:and/model/extension/message_extra.dart';
import 'package:and/model/extension/reactions_request.dart';
import 'package:and/model/extension/reminder_ext.dart';
import 'package:and/model/extension/sync_msg_ext.dart';
import 'package:and/model/extension/sync_reaction_ext.dart';
import 'package:and/model/extension/user_info_ext.dart';
import 'package:and/model/group.dart';
import 'package:and/model/group_detail.dart';
import 'package:and/model/invite_code.dart';
import 'package:and/model/online_user_and_device.dart';
import 'package:and/model/request/add_sticker_request.dart';
import 'package:and/model/request/apply_friend_request.dart';
import 'package:and/model/request/call_request.dart';
import 'package:and/model/request/clear_unread_request.dart';
import 'package:and/model/request/create_group_request.dart';
import 'package:and/model/request/delete_msg_request.dart';
import 'package:and/model/request/email_register_request.dart';
import 'package:and/model/request/group_member_request.dart';
import 'package:and/model/request/message_extra_sync_request.dart';
import 'package:and/model/request/message_pinned_request.dart';
import 'package:and/model/request/message_readed_request.dart';
import 'package:and/model/request/offset_msg_request.dart';
import 'package:and/model/request/phone_register_request.dart';
import 'package:and/model/request/pwd_forget_mail_request.dart';
import 'package:and/model/request/pwd_forget_sms_request.dart';
import 'package:and/model/request/send_forget_pwd_sms_request.dart';
import 'package:and/model/request/send_mail_request.dart';
import 'package:and/model/request/send_sms_request.dart';
import 'package:and/model/request/sync_channel_msg_request.dart';
import 'package:and/model/request/sync_conversation_extra_request.dart';
import 'package:and/model/request/sync_conversation_request.dart';
import 'package:and/model/request/sync_msg_request.dart';
import 'package:and/model/request/sync_reaction_request.dart';
import 'package:and/model/request/sync_reminder_request.dart';
import 'package:and/model/request/sync_unread_count_request.dart';
import 'package:and/model/request/syncack_request.dart';
import 'package:and/model/request/talk_to_any_one_request.dart';
import 'package:and/model/request/update_remark_request.dart';
import 'package:and/model/request/upload_devicetoken_request.dart';
import 'package:and/model/request/user_login_request.dart';
import 'package:and/model/request/visitor_register_request.dart';
import 'package:and/model/request/voice_recognize_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:and/model/response/send_sms_response.dart';
import 'package:and/model/scan_result.dart';
import 'package:and/model/sync_pinned_message.dart';
import 'package:and/model/upload_file.dart';
import 'package:and/model/user_badge.dart';
import 'package:and/model/user_info.dart';
import 'package:and/model/voice_recognize.dart';
import 'package:and/module/login/login_logic.dart';
import 'package:and/module/main/main_tab_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/deeplink_utils.dart';
import 'package:and/utils/device_utils.dart';
import 'package:and/utils/sms_util.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:uuid/uuid.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_voice_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'image_utils.dart';

class HttpUtils {
  static var lastMessageSeq = 0;

  static void updateChannels(List<WKChannel> channels) {
    WKIM.shared.channelManager.addOrUpdateChannels(channels);

    eventBus.fire(ContactSyncEvent());
  }

  static Future<WKChannel?> getChannel(
      String channelId, int channelType) async {
    try {
      final channel = (await ChannelApi(MyHttp.dio)
          .getChannel(channelID: channelId, channelType: channelType));
      var wkChannel = channel.toWKChannel();
      WKIM.shared.channelManager.addOrUpdateChannel(wkChannel);
      return wkChannel;
    } catch (e) {
      print("获取频道出错: $e");
      return null;
    }
  }

  static Future<ChannelInfo?> getChannelInfo(
      String channelId, int channelType) async {
    try {
      final channelInfo = (await ChannelApi(MyHttp.dio)
          .getChannel(channelID: channelId, channelType: channelType));
      return channelInfo;
    } catch (e) {
      print("获取频道信息出错: $e");
      return null;
    }
  }

  static Future<bool> addOrRemoveBlackList(String uid, bool isAdd) async {
    try {
      final result = isAdd
          ? (await UserApi(MyHttp.dio).addBlackList(uid))
          : (await UserApi(MyHttp.dio).removeBlackList(uid));
      return result.success;
    } catch (e) {
      return false;
    }
  }

  /// 发起单聊通话
  static Future<CallResponse?> startSingleCall(
      String channelID, int callType) async {
    try {
      final result = (await ChannelApi(MyHttp.dio).startSingleCall(
        channelID: channelID,
        callType: callType,
      ));
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 发起群聊通话
  static Future<CallResponse?> startGroupCall(String channelID) async {
    try {
      final result = await ChannelApi(MyHttp.dio).startGroupCall(
        channelID: channelID,
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 获取群聊token
  static Future<String?> getGroupCallToken(String channelID) async {
    try {
      final result = await ChannelApi(MyHttp.dio).getGroupCallToken(
        channelID: channelID,
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 加入通话
  static Future<CommonResponse?> joinCall(
      String channelID, int channelType, CallRequest request) async {
    try {
      final result = await ChannelApi(MyHttp.dio).joinCall(
        channelID: channelID,
        channelType: channelType,
        body: request,
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 拒绝通话
  static Future<CommonResponse?> rejectCall(
      String channelID, int channelType, CallRequest request) async {
    try {
      final result = await ChannelApi(MyHttp.dio).rejectCall(
        channelID: channelID,
        channelType: channelType,
        body: request,
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 挂断通话
  static Future<CommonResponse?> hangupCall(
      String channelID, int channelType, CallRequest request) async {
    try {
      final result = await ChannelApi(MyHttp.dio).hangupCall(
        channelID: channelID,
        channelType: channelType,
        body: request,
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 取消通话
  static Future<CommonResponse?> cancelCall(
      String channelID, int channelType, CallRequest request) async {
    try {
      final result = await ChannelApi(MyHttp.dio).cancelCall(
        channelID: channelID,
        channelType: channelType,
        body: request,
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 获取对方设置
  static Future<ChannelInfo?> getCounterpartSetting(
      String channelID, int channelType) async {
    try {
      final result = await ChannelApi(MyHttp.dio).getCounterpartSetting(
        channelID: channelID,
        channelType: channelType,
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  static Future<bool> deleteFriend(String uid) async {
    try {
      final result = (await FriendApi(MyHttp.dio).deleteFriend(uid));
      return result.success;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> updatePin(
    String channelID,
    int channelType,
    bool isPin,
  ) async {
    var result = false;
    if (channelType == WKChannelType.personal) {
      result =
          await HttpUtils.updateUserSetting(channelID, "top", isPin ? 1 : 0);
    } else if (channelType == WKChannelType.group) {
      result =
          await HttpUtils.updateGroupSetting(channelID, "top", isPin ? 1 : 0);
    }
    return result;
  }

  static Future<bool> clearHistory(String channelID, int channelType,
      {bool clearBoth = false, bool isDeleteCov = false}) async {
    var channel =
        await WKIM.shared.channelManager.getChannel(channelID, channelType);
    if (channel == null) {
      return false;
    }
    await HttpUtils.offsetMsg(channelID, channelType, both: clearBoth);
    //清除消息
    WKIM.shared.messageManager.clearWithChannel(channelID, channelType);
    //更新小红点数量
    await HttpUtils.clearUnread(channelID, channelType, unread: 0);

    // reminder都设置为已完成
    var reminderUnDoneList = await WKIM.shared.reminderManager
        .getWithChannel(channelID, channelType, 0);
    for (var reminder in reminderUnDoneList) {
      reminder.done = 1;
    }
    WKIM.shared.reminderManager.saveOrUpdateReminders(reminderUnDoneList);


    if (isDeleteCov) {
      if (channelType == WKChannelType.personal) {
        //更新channel，设置为未关注
        channel.follow = 0;
        WKIM.shared.channelManager.addOrUpdateChannel(channel);
      }

      //清除会话
      var result = await WKIM.shared.conversationManager
          .deleteMsg(channelID, channelType);
      if (result) {
        //解除置顶
        updatePin(channelID, channelType, false);
      }
    }
    return true;
  }

  /// both是否双向删除
  static Future<bool> offsetMsg(String channelId, int channelType,
      {bool both = false}) async {
    try {
      int msgSeq = await WKIM.shared.messageManager
          .getMaxMessageSeq(channelId, channelType);
      final CommonResponse result;
      if (both) {
        result = (await MessageApi(MyHttp.dio).offsetMsgBoth(OffsetMsgRequest(
            channelId: channelId,
            channelType: channelType,
            messageSeq: msgSeq)));
      } else {
        result = (await MessageApi(MyHttp.dio).offsetMsg(OffsetMsgRequest(
            channelId: channelId,
            channelType: channelType,
            messageSeq: msgSeq)));
      }

      return result.success;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> exitGroup(String groupNo) async {
    try {
      final result = (await GroupApi(MyHttp.dio).exitGroup(groupNo));
      return result.success;
    } catch (e) {
      print("退出群组出错: $e");
      return false;
    }
  }

  static Future<bool> applyFriend(
      String toUid, String vercode, String remark) async {
    try {
      final result = (await FriendApi(MyHttp.dio).applyAddFriend(
          ApplyFriendRequest(toUid: toUid, remark: remark, vercode: vercode)));
      return result.success;
    } catch (e) {
      print("申请好友出错: $e");
      return false;
    }
  }

  static Future<bool> talkToAnyOne(String toUid, String remark) async {
    try {
      final result = (await UserApi(MyHttp.dio).talkToAnyOne(
          TalkToAnyOneRequest(receivedUid: toUid, content: remark)));

      return result.success;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> updateRemark(String uid, String remark) async {
    try {
      final result = (await FriendApi(MyHttp.dio)
          .updateFriendRemark(UpdateRemarkRequest(uid: uid, remark: remark)));
      return result.success;
    } catch (e) {
      print("更新备注出错: $e");
      return false;
    }
  }

  static Future<ScanResult?> scanResult(String url) async {
    try {
      ScanResult result;
      if (url.startsWith("http")) {
        result = (await CommonApi(MyHttp.dio).getScanResult(
          url: url,
        ));
      } else {
        result = (await CommonApi(MyHttp.dio).getQrcode(
          code: url,
        ));
      }
      return result;
    } catch (e) {
      print("扫描二维码出错: $e");
      return null;
    }
  }

  static Future<String?> uploadChannelFile(
      String channelID, int channelType, String filePath,
      {String type = "chat", bool isLarge = false}) async {
    File file = File(filePath);
    String prefix = file.ext;
    int timestampMilliseconds = DateTime.now().millisecondsSinceEpoch;
    String rootPath = "/$channelType/$channelID/$timestampMilliseconds$prefix";
    return uploadFile(rootPath, filePath, type: type, isLarge: isLarge);
  }

  static Future<String?> uploadFile(String uploadPath, String filePath,
      {String type = "chat", bool isLarge = false}) async {
    try {
      File file = File(filePath);
      final mimeType = lookupMimeType(file.path);
      int width = 0;
      int height = 0;
      if (mimeType?.startsWith("image/") ?? false) {
        var imageInfo = await ImageUtils.getImageInfo(file);
        width = imageInfo.width;
        height = imageInfo.height;
      }
      final uploadUrl = (await FileApi(MyHttp.dio).getUploadUrl(
              path: uploadPath,
              type: type,
              isLarge: isLarge,
              height: height,
              width: width))
          .url;

      /// define the multi-part upload task (subset of parameters shown)
      var headers = await MyHttp.addHeaders();

      final task = UploadTask(
          url: uploadUrl,
          directory: file.parent.path,
          baseDirectory: BaseDirectory.root,
          filename: Uri.decodeFull(file.name),
          headers: headers,
          updates:
              Updates.statusAndProgress // request status and progress updates
          );

// Start upload, and wait for result. Show progress and status changes
// while uploading
      final downloader = FileDownloader();
      final result = await downloader.upload(task,
          onProgress: (progress) => print('Progress: ${progress * 100}%'),
          onStatus: (status) => print('Status: $status'));
      if (result.status == TaskStatus.complete && result.responseBody != null) {
        UploadFile uploadFile =
            UploadFile.fromJson(jsonDecode(result.responseBody!));
        return uploadFile.path;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<bool> uploadAvatar(String uid, String filePath) async {
    try {
      final response = (await FileApi(MyHttp.dio).uploadAvatar(
        uid: uid,
        file: File(filePath),
      ));
      return response.success;
    } catch (e) {
      print("上传头像出错: $e");
      return false;
    }
  }

  static Future<bool> syncFriendApplyCount() async {
    try {
      final response = await UserApi(MyHttp.dio).friendApplyCount();
      NaviCountBloc.notify(TabType.contacts, response.count);
    } catch (e) {
      print('同步申请好友数量异常');
      return false;
    }

    return true;
  }

  static Future<bool> deleteFriendApplyCount() async {
    try {
      await UserApi(MyHttp.dio).deleteApplyCount();
      NaviCountBloc.notify(TabType.contacts, 0);
    } catch (e) {
      print('同步申请好友数量异常');
      return false;
    }

    return true;
  }

  static Future<bool> syncOnlineUsers() async {
    try {
      final response = await UserApi(MyHttp.dio).online();
      var channels = await WKIM.shared.channelManager
          .getWithFollowAndStatus(WKChannelType.personal, 1, 1);
      Map<String, FriendOnline> friendOnlineMap = {};
      for (var friend in response.friends) {
        friendOnlineMap[friend.uid] = friend;
      }
      //TODO save pc online

      //更新好友状态
      for (var channel in channels) {
        var friend = friendOnlineMap[channel.channelID];
        if (friend != null) {
          channel.online = friend.online;
          channel.lastOffline = friend.lastOffline;
        } else {
          channel.online = 0;
        }
      }
      updateChannels(channels);
    } catch (e) {
      print('同步好友在线状态异常');
      return false;
    }

    return true;
  }

  static Future<bool> syncFriends({int version = 0}) async {
    try {
      final response = await FriendApi(MyHttp.dio).syncFriends(version, 500, 1);
      List<WKChannel> channels = [];

      var tempVersion = 0;

      for (var item in response) {
        WKChannel channel = item.toWKChannel();
        channels.add(channel);
        if (item.version > tempVersion) {
          tempVersion = item.version;
        }
      }
      if (channels.isNotEmpty) {
        updateChannels(channels);

        // 延迟500毫秒再执行，判断是否还有未同步的通讯录
        await Future.delayed(const Duration(milliseconds: 500), () {
          syncFriends(version: tempVersion);
        });
      } else {
        CacheHelper.saveSyncFriend(false);
      }
    } catch (e) {
      print('同步通讯录异常');
      return false;
    }

    return true;
  }

  static Future<bool> syncConversationExtra() async {
    try {
      int version = await WKIM.shared.conversationManager.getExtraMaxVersion();
      final response = await ConversationApi(MyHttp.dio)
          .syncCoverExtra(SyncConversationExtraRequest(version: version));
      var msgExtras =
          response.map((e) => e.toWKConversationMsgExtra()).toList();

      WKIM.shared.conversationManager.saveSyncMsgExtras(msgExtras);
    } catch (e) {
      print('同步会话扩展异常');
      return false;
    }

    return true;
  }

  static Future<bool> syncReminder() async {
    try {
      int version = await WKIM.shared.reminderManager.getMaxVersion();
      var channelIDs = (await WKIM.shared.conversationManager.getAll())
          .where((e) => e.channelType == WKChannelType.group)
          .map((e) => e.channelID)
          .toList();

      var loginUid = CacheHelper.uid ?? '';
      final response = await MessageApi(MyHttp.dio).syncReminder(
          SyncReminderRequest(
              version: version, limit: 200, channelIds: channelIDs));
      var reminders = response.map((e) {
        var item = e.toWKReminder();
        if (item.publisher.isNotEmpty && item.publisher == loginUid) {
          item.done = 1;
        }
        return item;
      }).toList();
      await WKIM.shared.reminderManager.saveOrUpdateReminders(reminders);
    } catch (e) {
      print('同步会话扩展异常');
      return false;
    }

    return true;
  }

  static Future<bool> doneReminder(List<int> ids) async {
    if (ids.isEmpty) return false;
    try {
      await MessageApi(MyHttp.dio)
          .doneReminder(ids.map((e) => e.toString()).toList());
    } catch (e) {
      print('reminder更新失败');
      return false;
    }

    return true;
  }

  static Future<bool> doneReaded(
      String channelId, int channelType, List<String> ids) async {
    if (ids.isEmpty) return false;
    try {
      await MessageApi(MyHttp.dio).readed(MessageReadedRequest(
        channelId: channelId,
        channelType: channelType,
        messageIds: ids,
      ));
    } catch (e) {
      print('已读消息更新失败');
      return false;
    }

    return true;
  }

  static Future<bool> syncConversation(String lastSsgSeqs, int msgCount,
      int version, Function(WKSyncConversation) back) async {
    var deviceUid = await DeviceUtils.getDeviceId();
    final response = await ConversationApi(MyHttp.dio).syncConversation(
      SyncConversationRequest(
        version: version,
        lastMsgSeqs: lastSsgSeqs,
        msgCount: 10,
        deviceUuid: deviceUid,
      ),
    );

    var conversations = response.conversations;
    for (var conversation in conversations) {
      eventBus.fire(ConversationSyncEvent(conversation));
    }

    WKSyncConversation syncConversation = response.toWKSyncConversation();
    back(syncConversation);

    lastMessageSeq = 0;
    syncCmdMsgs(0);
    ackDeviceUUID();
    syncReminder();

    return true;
  }

  static Future<bool> syncChannelMsg(
      String channelID,
      int channelType,
      int startMsgSeq,
      int endMsgSeq,
      int limit,
      int pullMode,
      Function(WKSyncChannelMsg) back) async {
    try {
      final response = await MessageApi(MyHttp.dio).syncChannelMsg(
        SyncChannelMsgRequest(
          channelId: channelID,
          channelType: channelType,
          startMessageSeq: startMsgSeq,
          endMessageSeq: endMsgSeq,
          limit: limit,
          pullMode: pullMode,
        ),
      );

      WKSyncChannelMsg msg = response.toWKSyncChannelMsg();
      back(msg);
    } catch (e) {
      print(
          "同步频道消息异常:$e, pullMode:$pullMode, startSeq:$startMsgSeq, endSeq:$endMsgSeq");
      if (kDebugMode) {
        showToast(
            "同步频道消息异常, pullMode:$pullMode, startSeq:$startMsgSeq, endSeq:$endMsgSeq");
      }
      return false;
    }

    return true;
  }

  static Future<bool> syncGroupMembers(String groupNo,
      {int? maxVersion}) async {
    try {
      int version = maxVersion ??
          await WKIM.shared.channelMemberManager
              .getMaxVersion(groupNo, WKChannelType.group);
      final response =
          await GroupApi(MyHttp.dio).syncGroupMembers(groupNo, 1000, version);
      var members = response.map((e) => e.toWKChannelMember()).toList();
      WKIM.shared.channelMemberManager.saveOrUpdateList(members);

      if (members.isNotEmpty) {
        // 延迟500毫秒再执行，判断是否还有未同步的用户
        await Future.delayed(const Duration(milliseconds: 500), () {
          syncGroupMembers(groupNo);
        });
      }
    } catch (e) {
      print('同步群组用户异常');
      return false;
    }

    return true;
  }

  static Future<bool> addGroupMembers(String groupNo, List<String> ids) async {
    try {
      final response = await GroupApi(MyHttp.dio)
          .addGroupMembers(groupNo, GroupMemberRequest(members: ids));
      return response.success;
    } catch (e) {
      print('添加群成员失败');
    }
    return false;
  }

  static Future<bool> deleteGroupMembers(
      String groupNo, List<String> ids) async {
    try {
      final response = await GroupApi(MyHttp.dio)
          .deleteGroupMembers(groupNo, GroupMemberRequest(members: ids));

      var list = ids.map((id) {
        var member = WKChannelMember();
        member.channelID = groupNo;
        member.channelType = WKChannelType.group;
        member.memberUID = id;
        member.isDeleted = 1;
        return member;
      }).toList();
      WKIM.shared.channelMemberManager.saveOrUpdateList(list);
      return response.success;
    } catch (e) {
      print('删除群成员失败');
    }
    return false;
  }

  static Future<Group?> getGroupInfo(String groupNo) async {
    try {
      final response = await GroupApi(MyHttp.dio).getGroupInfo(groupNo);
      return response;
    } catch (e) {
      print('获取群信息失败');
    }
    return null;
  }

  static Future<GroupDetail?> getGroupDetail(String groupNo) async {
    try {
      final response = await GroupApi(MyHttp.dio).getGroupDetail(groupNo);
      return response;
    } catch (e) {
      print('获取群信息失败');
    }
    return null;
  }

  static Future<UserInfo?> getUserInfo(String uid, {String? groupNo}) async {
    try {
      final response = await UserApi(MyHttp.dio).getUserInfo(uid, groupNo);
      return response;
    } catch (e) {
      print('获取用户信息失败$e');
    }

    return null;
  }

  static Future<void> revokeMsg(String clientMsgNo, String msgId,
      String channelId, int channelType) async {
    try {
      MessageApi(MyHttp.dio).revokeMsg(
        clientMsgNo: clientMsgNo,
        messageId: msgId,
        channelId: channelId,
        channelType: channelType,
      );
      print('撤回消息成功');
    } catch (e) {
      print('撤回消息失败$e');
    }
  }

  static Future<bool> deleteMsg(List<WKMsg> msgList,
      {required String channelID,
      required int channelType,
      bool both = false}) async {
    try {
      var requests = msgList.map((e) {
        var request = DeleteMsgRequest(
          channelId: channelID,
          channelType: channelType,
          messageSeq: e.messageSeq,
          messageId: e.messageID,
        );
        return request;
      }).toList();
      if (both) {
        await MessageApi(MyHttp.dio).deleteMsgAll(requests);
      } else {
        await MessageApi(MyHttp.dio).deleteMsg(requests);
      }

      var clientMsgNos = msgList.map((e) => e.clientMsgNO).toList();
      await WKIM.shared.messageManager.deleteWithClientMsgNos(clientMsgNos);
      return true;
    } catch (e) {
      print('删除消息失败$e');
      return false;
    }
  }

  static Future<void> syncMsgExtra(String channelId, int channelType) async {
    try {
      int maxExtraVersion = await WKIM.shared.messageManager
          .getMaxExtraVersionWithChannel(channelId, channelType);
      var deviceUid = await DeviceUtils.getDeviceId();
      var response = await MessageApi(MyHttp.dio).syncExtraMsg(
          body: MessageExtraSyncRequest(
        channelId: channelId,
        channelType: channelType,
        extraVersion: maxExtraVersion,
        source: deviceUid,
        limit: 100,
      ));
      List<WKMsgExtra> list = [];
      for (int i = 0; i < response.length; i++) {
        var extraJson = response[i];
        WKMsgExtra extra = extraJson.toWKMsgExtra(channelId, channelType);
        list.add(extra);
      }
      if (response.isNotEmpty) {
        WKIM.shared.messageManager.saveRemoteExtraMsg(list);
        // 延迟500毫秒再执行，判断是否还有未同步的数据
        Future.delayed(const Duration(milliseconds: 500), () {
          syncMsgExtra(channelId, channelType);
        });
      }
    } catch (e) {
      print('同步消息扩展失败$e');
    }
  }

  // 清空红点
  static Future<bool> clearUnread(String channelId, int channelType,
      {int unread = 0}) async {
    try {
      if (unread < 0) unread = 0;
      WKIM.shared.conversationManager
          .updateRedDot(channelId, channelType, unread);
      await ConversationApi(MyHttp.dio).clearUnread(ClearUnreadRequest(
        channelId: channelId,
        channelType: channelType,
        unread: unread,
      ));
      print('清空红点成功');
    } catch (e) {
      print('清空红点失败$e');
      return false;
    }
    return true;
  }

  // 清除频道消息
  static Future<void> clearChannelMsg(String channelId, int channelType) async {
    try {
      int maxSeq = await WKIM.shared.messageManager
          .getMaxMessageSeq(channelId, channelType);
      MessageApi(MyHttp.dio).offsetMsg(OffsetMsgRequest(
        channelId: channelId,
        channelType: channelType,
        messageSeq: maxSeq,
      ));
      WKIM.shared.messageManager.clearWithChannel(channelId, channelType);
    } catch (e) {
      print('清除频道消息失败$e');
    }
  }

  // 创建群
  static Future<WKChannel?> createGroup(
      String name, List<String> ids, List<String> names) async {
    try {
      var group = await GroupApi(MyHttp.dio).createGroup(CreateGroupRequest(
        name: name,
        members: ids,
        memberNames: names,
      ));

      var channel = group.toWKChannel();
      WKIM.shared.channelManager.addOrUpdateChannel(channel);
      return channel;
    } catch (e) {
      print('创建群失败$e');
      return null;
    }
  }

  static Future<bool> updateGroupForbidden(String groupNo, int value) async {
    try {
      await GroupApi(MyHttp.dio).groupForbidden(groupNo, value);
      return true;
    } catch (e) {
      print('修改群禁言失败');
      return false;
    }
  }

  static Future<bool> updateGroupSetting(
      String groupNo, String key, dynamic value) async {
    try {
      await GroupApi(MyHttp.dio).updateGroupSetting(groupNo, {
        key: value,
      });
      return true;
    } catch (e) {
      print('修改群设置信息失败');
      return false;
    }
  }

  static Future<bool> updateGroupInfo(
      String groupNo, String key, dynamic value) async {
    try {
      await GroupApi(MyHttp.dio).updateGroupInfo(groupNo, {
        key: value,
      });
      return true;
    } catch (e) {
      print('修改群信息失败');
      return false;
    }
  }

  static Future<bool> updateGroupMemberInfo(
      String groupNo, String uid, String key, dynamic value) async {
    try {
      await GroupApi(MyHttp.dio).updateGroupMemberInfo(groupNo, uid, {
        key: value,
      });
      return true;
    } catch (e) {
      print('修改群成员信息失败');
      return false;
    }
  }

  /// 修改对某个好友的设置
  static Future<bool> updateUserSetting(
      String channelId, String key, dynamic value) async {
    try {
      await UserApi(MyHttp.dio).updateUserSetting(channelId, {
        key: value,
      });
      return true;
    } catch (e) {
      print('修改用户信息失败');
      return false;
    }
  }

  /// 修改登录用户设置
  static Future<bool> updateMySetting(String key, dynamic value) async {
    try {
      await UserApi(MyHttp.dio).updateMySetting({
        key: value,
      });
      return true;
    } catch (e) {
      print('修改用户信息失败');
      return false;
    }
  }

  /// 获取邀请码
  static Future<InviteInfo?> getInviteCode() async {
    try {
      final response = await UserApi(MyHttp.dio).getInviteInfo({});

      CacheHelper.saveInviteInfo(response);
      return response;
    } catch (e) {
      print('获取邀请码失败');
    }
    return null;
  }

  static Future<void> login(
      LoginType loginType, String username, String password,
      {String areaCode = ""}) async {
    var name = loginType == LoginType.phone ? areaCode + username : username;

    UserLoginRequest request = UserLoginRequest(
      username: name,
      password: pwdHash(password),
      device: Device(
        deviceId: await DeviceUtils.getDeviceId(),
        deviceName: await DeviceUtils.getDeviceName(),
        deviceModel: await DeviceUtils.getDeviceModel(),
      ),
    );

    await EasyLoadingHelper.show(onAction: () async {
      try {
        var response = await UserApi(MyHttp.dio).login(request);
        CommonHelper.loginAndLaunchHome(response);
      } catch (e) {
        print(e);
      }
    });
  }

  static Future<bool> deleteDeviceToken() async {
    try {
      CommonResponse response = await UserApi(MyHttp.dio).deleteDeviceToken();
      return response.success;
    } catch (e) {
      print('删除设备失败');
    }

    return false;
  }

  static Future<void> visitorRegister(String password, String code,
      {String? email, String? phone, String? zone}) async {
    VisitorRegisterRequest request = VisitorRegisterRequest(
      verifyCode: code,
      areaCode: zone,
      phone: phone ?? '',
      email: email ?? '',
      password: pwdHash(password),
    );

    await EasyLoadingHelper.show(onAction: () async {
      await UserApi(MyHttp.dio).visitorRegister(request);
      if (email != null) {
        login(LoginType.email, email, password);
      } else {
        login(LoginType.phone, phone ?? '', password, areaCode: zone ?? '');
      }
    });
  }

  static Future<void> emailRegister(
      String email, String password, String code) async {
    var isVisitor = DeeplinkUtils.visitorToken != null;
    if (isVisitor) {
      return visitorRegister(
        password,
        code,
        email: email,
      );
    }
    EmailRegisterRequest request = EmailRegisterRequest(
      email: email,
      password: pwdHash(password),
      code: code,
      flag: 0,
      device: Device(
        deviceId: await DeviceUtils.getDeviceId(),
        deviceName: await DeviceUtils.getDeviceName(),
        deviceModel: await DeviceUtils.getDeviceModel(),
      ),
    );
    await EasyLoadingHelper.show(onAction: () async {
      try {
        await UserApi(MyHttp.dio).emailRegister(request);
        login(LoginType.email, email, password);
      } catch (e) {
        print(e);
      }
    });
  }

  static Future<void> phoneRegister(
      {required String zone,
      required String phone,
      required String password,
      required String code,
      String? inviteCode}) async {
    var isVisitor = DeeplinkUtils.visitorToken != null;
    if (isVisitor) {
      return visitorRegister(password, code, phone: phone, zone: zone);
    }
    PhoneRegisterRequest request = PhoneRegisterRequest(
      zone: zone,
      phone: phone,
      password: pwdHash(password),
      code: code,
      flag: 0,
      inviteCode: inviteCode ?? '',
      device: Device(
        deviceId: await DeviceUtils.getDeviceId(),
        deviceName: await DeviceUtils.getDeviceName(),
        deviceModel: await DeviceUtils.getDeviceModel(),
      ),
    );
    await EasyLoadingHelper.show(onAction: () async {
      try {
        await UserApi(MyHttp.dio).phoneRegister(request);
        login(LoginType.phone, phone, password, areaCode: zone);
      } catch (e) {
        print(e);
      }
    });
  }

  static Future<void> sendMail(String mail,
      {required SendMailType mailType, required Function() onSuccess}) async {
    try {
      SendMailRequest request = SendMailRequest(
        email: mail,
        type: mailType,
      );

      EasyLoadingHelper.show(onAction: () async {
        var response =
            await ShareApi(MyHttp.dio).sendMail(request).catchError((e) {
          EasyLoading.showError(globalContext?.l10n.sendCodeFailedDesc ?? "");
          return CommonResponse(
              status: e.response?.statusCode ?? -1, msg: e.toString());
        });
        if (response.success) {
          EasyLoading.showSuccess(
              globalContext?.l10n.signupVerifyCodeDesc(mail) ?? "");
          onSuccess();
        } else {
          EasyLoading.showError(response.msg ?? "");
        }
      });
    } catch (e) {
      print(e);
    }
  }

  static Future<void> sendSMS(
      {required String phone,
      required String zone,
      required SendCodeType codeType,
      required Function() onSuccess}) async {
    try {
      SendSMSRequest request = SendSMSRequest(
        zone: zone,
        phone: phone,
        codeType: codeType,
      );

      if (!await SmsUtil.verifyCaptchaBeforeSendSms(globalContext)) return;
      EasyLoadingHelper.show(onAction: () async {
        var response =
            await UserApi(MyHttp.dio).sendSMS(request).catchError((e) {
          EasyLoading.showError(globalContext?.l10n.sendCodeFailedDesc ?? "");
          return SMSResponse(exist: 0);
        });
        if (codeType == SendCodeType.register ||
            codeType == SendCodeType.updatePhone) {
          if (response.exist == 1) {
            EasyLoading.showError(globalContext?.l10n.registerGoLogin ?? "");
            return;
          }
        }
        EasyLoading.showSuccess(
            globalContext?.l10n.signupVerifyCodeDesc(phone) ?? "");
        onSuccess();
      });
    } catch (e) {
      print(e);
    }
  }

  static Future<void> sendForgetSms(
      {required String phone,
      required String zone,
      required Function() onSuccess}) async {
    try {
      SendForgetPwdSMSRequest request =
          SendForgetPwdSMSRequest(zone: zone, phone: phone);

      if (!await SmsUtil.verifyCaptchaBeforeSendSms(globalContext)) return;

      EasyLoadingHelper.show(onAction: () async {
        await UserApi(MyHttp.dio).sendForgetPwdSMS(request).catchError((e) {
          EasyLoading.showError(globalContext?.l10n.sendCodeFailedDesc ?? "");
          return SMSResponse(exist: 0);
        });
        EasyLoading.showSuccess(
            globalContext?.l10n.signupVerifyCodeDesc(phone) ?? "");
        onSuccess();
      });
    } catch (e) {
      print(e);
    }
  }

  static Future<void> pwdForgetSms(
      String phone, String zone, String password, String code) async {
    try {
      PwdForgetSmsRequest request = PwdForgetSmsRequest(
        phone: phone,
        zone: zone,
        pwd: pwdHash(password),
        code: code,
      );

      EasyLoadingHelper.show(onAction: () async {
        var response = await UserApi(MyHttp.dio).pwdForget(request);
        if (response.success) {
          login(LoginType.phone, phone, password, areaCode: zone);
        }
      });
    } catch (e) {
      print(e);
    }
  }

  static Future<void> pwdForgetEmail(
      String email, String password, String code) async {
    try {
      PwdForgetMailRequest request = PwdForgetMailRequest(
        email: email,
        pwd: pwdHash(password),
        code: code,
      );

      EasyLoadingHelper.show(onAction: () async {
        var response = await UserApi(MyHttp.dio).pwdForgetEmail(request);
        if (response.success) {
          login(LoginType.email, email, password);
        }
      });
    } catch (e) {
      print(e);
    }
  }

  static String pwdHash(String password) {
    var newPwd = "gg$password";

    return generateMd5(newPwd);
  }

  static String generateMd5(String input) {
    return md5.convert(utf8.encode(input)).toString();
  }

  static Future<bool?> updateInfo(String field, dynamic value) async {
    try {
      final result = (await UserApi(MyHttp.dio).update({field: value}));
      return result.success;
    } catch (e) {
      print('更新信息失败: $e');
      return false;
    }
  }

  static Future<bool> uploadDeviceToken(String? deviceToken) async {
    try {
      var pushType =
          Platform.isIOS ? CommonKeys.pushTypeIos : CommonKeys.pushTypeFirebase;
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      String bundleId = packageInfo.packageName;
      UploadDevicetokenRequest request = UploadDevicetokenRequest(
          device_token: deviceToken ?? '',
          device_type: pushType,
          bundle_id: bundleId);
      final response = (await UserApi(MyHttp.dio).uploadDeviceToken(request));
      return response.success;
    } catch (e) {
      print('上传设备token失败: $e');
      return false;
    }
  }

  static Future<bool> updateBadge(int badge) async {
    try {
      final result =
          (await UserApi(MyHttp.dio).updateBadge(UserBadge(badge: badge)));
      return result.success;
    } catch (e) {
      print('更新角标失败: $e');
      return false;
    }
  }

  static Future<InviteInfo?> modifyInviteCode(
      String field, dynamic value) async {
    try {
      final result =
          (await UserApi(MyHttp.dio).customInviteInfo({field: value}));
      return result;
    } catch (e) {
      print('修改邀请码失败: $e');
      return null;
    }
  }

  /// 注销账号
  static Future<bool> destroyAccount(String code) async {
    try {
      EasyLoading.show();
      final response =
          await UserApi(MyHttp.dio).destroyAccount(code).catchError((e) {
        EasyLoading.dismiss();
        return CommonResponse(
            status: e.response?.statusCode ?? -1, msg: e.toString());
      });
      EasyLoading.dismiss();
      return response.success;
    } catch (e) {
      print(e);
      return false;
    }
  }

  /// 获取注销账号验证码
  static Future<bool> sendDestroyAccountSMS() async {
    try {
      if (!await SmsUtil.verifyCaptchaBeforeSendSms(globalContext)) {
        return false;
      }

      EasyLoading.show();
      final response =
          await UserApi(MyHttp.dio).destroyAccountSMS().catchError((e) {
        EasyLoading.dismiss();
        return CommonResponse(
            status: e.response?.statusCode ?? -1, msg: e.toString());
        ;
      });
      EasyLoading.dismiss();
      return response.success;
    } catch (e) {
      print(e);
      return false;
    }
  }

  //TODO handle cmd
  static Future<void> syncCmdMsgs(int maxMessageSeq) async {
    try {
      var msgList = await MessageApi(MyHttp.dio)
          .syncMsg(SyncMsgRequest(limit: 500, maxMessageSeq: maxMessageSeq));

      if (msgList.isNotEmpty) {
        for (var msg in msgList) {
          // 处理消息
          WKSyncMsg syncMsg = msg.toWKSyncMsg();
          WKMsg wkMsg = syncMsg.getWKMsg();
          if (wkMsg.messageSeq > lastMessageSeq) {
            lastMessageSeq = wkMsg.messageSeq;
          }
        }
        if (lastMessageSeq != 0) {
          MessageApi(MyHttp.dio).ackMsg(lastMessageSeq);
        }
        if (lastMessageSeq > maxMessageSeq) {
          syncCmdMsgs(lastMessageSeq);
        }
      } else {
        if (lastMessageSeq != 0) {
          MessageApi(MyHttp.dio).ackMsg(lastMessageSeq);
        }
      }
    } catch (e) {
      print('同步命令消息失败: $e');
    }
  }

  static void ackDeviceUUID() async {
    try {
      var deviceUid = await DeviceUtils.getDeviceId();
      ConversationApi(MyHttp.dio)
          .ackCoverMsg(SyncackRequest(deviceUuid: deviceUid));
    } catch (e) {
      print('确认设备UUID失败: $e');
    }
  }

  static void voiceRecognize(
      WKMsg msg, Function(VoiceRecognize? recognize) onBack) async {
    var voiceContent = msg.messageContent as WKVoiceContent;
    var recognizeResult = voiceContent.recognizeResult;
    if (recognizeResult != null && recognizeResult.isNotEmpty) {
      var recognize = VoiceRecognize.fromJson(jsonDecode(recognizeResult));
      onBack(recognize);

      return;
    }

    final dio = MyHttp.dio;
    final url = HttpConfig.getApiUrl() + "/message/voice/recognize";
    final _data = <String, dynamic>{};
    var request = VoiceRecognizeRequest(
      messageId: msg.messageID,
      channelId: msg.channelID,
      channelType: msg.channelType,
      messageSeq: msg.messageSeq,
    );
    _data.addAll(request.toJson());

    try {
      //初始化消息
      VoiceRecognize localRecognize = VoiceRecognize(event: SteamEvent.begin);
      onBack(localRecognize);

      final response = await dio.post<ResponseBody>(url,
          data: _data,
          options: Options(
            headers: {
              'Accept': 'text/event-stream',
              'Cache-Control': 'no-cache',
            },
            responseType: ResponseType.stream,
          ));

      String text = "";
      response.data?.stream.listen((data) {
        // 处理接收到的数据
        print("stream:${utf8.decode(data).trim()}");
        var results = utf8.decode(data).trim().split("\n");
        for (var result in results) {
          if (result.isEmpty) {
            continue;
          }
          if (result.startsWith("data:")) {
            var content = result.substring(5);
            VoiceRecognize recognize =
                VoiceRecognize.fromJson(jsonDecode(content));
            if (recognize.event == SteamEvent.begin) {
              // 语音识别开始
              onBack(recognize);
            } else if (recognize.event == SteamEvent.text) {
              // 语音识别中
              text += recognize.data?.text ?? '';

              VoiceRecognize localRecognize = VoiceRecognize(
                  event: recognize.event, data: Data(text: text));
              onBack(localRecognize);
            } else if (recognize.event == SteamEvent.end) {
              // 语音识别结束
              VoiceRecognize localRecognize = VoiceRecognize(
                  event: recognize.event, data: Data(text: text));
              WKVoiceContent messageContent =
                  msg.messageContent as WKVoiceContent;
              messageContent.recognizeResult =
                  jsonEncode(localRecognize.toJson());
              WKIM.shared.messageManager
                  .updateContent(msg.clientMsgNO, messageContent, true);

              onBack(localRecognize);
            }
          } else {
            var voiceResult = jsonDecode(result);
            var text = voiceResult['voice_recognize_result'] as String?;
            if (text != null) {
              WKVoiceContent messageContent =
                  msg.messageContent as WKVoiceContent;

              VoiceRecognize localRecognize =
                  VoiceRecognize(event: SteamEvent.end, data: Data(text: text));
              messageContent.recognizeResult =
                  jsonEncode(localRecognize.toJson());
              WKIM.shared.messageManager
                  .updateContent(msg.clientMsgNO, messageContent, true);

              onBack(localRecognize);
            }
          }
        }
      });
    } catch (e) {
      //初始化消息
      onBack(null);
    }
  }

  static Future<bool> reactionEmoji(
      String channelId, int channelType, String messageId, String emoji) async {
    try {
      await MessageApi(MyHttp.dio).reactions(ReactionsRequest(
          messageId: messageId,
          channelId: channelId,
          channelType: channelType,
          emoji: emoji));
      print('回应消息成功');
      return true;
    } catch (e) {
      print('回应消息失败$e');
    }

    return false;
  }

  static Future<bool> syncReactions(String channelId, int channelType,
      {int? seq}) async {
    try {
      seq ??= await WKIM.shared.messageManager
          .getMaxReactionSeq(channelId, channelType);
      var lastMessageSeq = seq;
      final response = await MessageApi(MyHttp.dio).syncReaction(
          SyncReactionRequest(
              seq: seq,
              channelId: channelId,
              channelType: channelType,
              limit: 500));
      var msgList = response.map((e) => e.toWKSyncMsgReaction()).toList();
      if (msgList.isNotEmpty) {
        for (var msg in msgList) {
          if (msg.seq > lastMessageSeq) {
            lastMessageSeq = msg.seq;
          }
        }

        WKIM.shared.messageManager.saveMessageReactions(msgList);
        syncReactions(channelId, channelType, seq: lastMessageSeq);
      }
    } catch (e) {
      print('同步响应扩展异常');
      return false;
    }

    return true;
  }

  static Future<AppVersion> checkAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      String version = packageInfo.version;
      String os = Platform.isIOS ? "iOS" : "Android";
      return await CommonApi(MyHttp.dio).checkVersion(os: os, version: version);
    } catch (e) {
      print('检查版本更新失败: $e');
      rethrow;
    }
  }

  /// 消息置顶
  static Future<void> pinMessage(
      String channelId, int channelType, String messageId, int messageSeq,
      {int type = 2}) async {
    try {
      MessageApi(MyHttp.dio).pinMessage(MessagePinnedRequest(
        channelId: channelId,
        channelType: channelType,
        messageId: messageId,
        messageSeq: messageSeq,
        type: type,
      ));
      WKIM.shared.messageManager.updateMsgPinned(messageId, true);
    } catch (e) {
      print('Failed to pin message: $e');
    }
  }

  /// 取消置顶消息
  static Future<void> unpinMessage(String channelId, int channelType,
      String messageId, int messageSeq, int type) async {
    try {
      MessageApi(MyHttp.dio).unpinMessage(MessagePinnedRequest(
        channelId: channelId,
        channelType: channelType,
        messageId: messageId,
        messageSeq: messageSeq,
        type: type,
      ));
      WKIM.shared.messageManager.updateMsgPinned(messageId, false);
    } catch (e) {
      print('Failed to unpin message: $e');
    }
  }

  /// 获取置顶消息列表
  static Future<SyncPinnedMessage?> syncPinnedMessages(
      String channelId, int channelType) async {
    try {
      var syncPinnedMessages = await MessageApi(MyHttp.dio)
          .syncPinnedMessages(MessagePinnedRequest(
        channelId: channelId,
        channelType: channelType,
        messageId: '',
        messageSeq: 0,
      ));
      var wkMsgExtraList = syncPinnedMessages.messages.map((msg) {
        return msg.toWKSyncMsg().getWKMsg().wkMsgExtra?? WKMsgExtra();
      }).toList();
      WKIM.shared.messageManager.saveRemoteExtraMsg(wkMsgExtraList);
      return syncPinnedMessages;
    } catch (e) {
      print('Failed to get pinned messages: $e');
      return null;
    }
  }

  /// 同步未读数量
  static Future<void> syncUnreadCount(String channelID, int channelType) async {
    try {
      var result =
          await MessageApi(MyHttp.dio).syncUnreadCount(SyncUnreadCountRequest(
        channelId: channelID,
        channelType: channelType,
      ));
      WKConversationMsg? uiConversion = await WKIM.shared.conversationManager
          .getMsgWithChannel(channelID, channelType);
      if (uiConversion != null) {
        uiConversion.unreadCount = result.unread;
        WKIM.shared.conversationManager.updateUnreadCount(uiConversion);
      }
    } catch (e) {
      print('Failed to sync unread count: $e');
      rethrow;
    }
  }

  /// 上传sticker
  static Future<bool> uploadSticker(List<File> files) async {
    var result = false;
    await EasyLoadingHelper.show(onAction: () async {
      try {
        for (var file in files) {
          String prefix = file.ext;
          String uploadPath = "/${Uuid().v4().toString()}$prefix";

          await HttpUtils.uploadFile(uploadPath, file.path, type: "sticker");
        }
        result = true;
      } catch (e) {
        e.printError();
      }
    });
    if (result) {
      showToast(globalContext!.l10n.addStickerSuccess);
    } else {
      showToast(globalContext!.l10n.addStickerFailed);
    }
    return result;
  }

  /// 上传sticker
  static Future<bool> addSticker(String filePath, int width, int height) async {
    var result = false;
    await EasyLoadingHelper.show(onAction: () async {
      try {
        var response = await UserApi(MyHttp.dio).addStickers(AddStickerRequest(
            filePath: filePath,
            width: width.toString(),
            height: height.toString()));
        result = response.success;
      } catch (e) {
        if (e is DioError) {
          if (e.response?.statusCode == 400) {
            var response = e.response?.data as Map<String, dynamic>;
            showToast(response['msg']);
          } else {
            showToast(globalContext!.l10n.addStickerFailed);
          }
        }
      }
    });
    return result;
  }
}
