import 'package:json_annotation/json_annotation.dart';

import 'message_extra.dart';

part 'reaction.g.dart';

@JsonSerializable()
class Reaction {
  @Json<PERSON><PERSON>(name: 'message_id', defaultValue: '')
  final String messageId;
  @J<PERSON><PERSON><PERSON>(defaultValue: '')
  final String uid;
  @Json<PERSON>ey(defaultValue: '')
  final String name;
  @J<PERSON><PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON>son<PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON>son<PERSON><PERSON>(defaultValue: 0)
  final int seq;
  @Json<PERSON>ey(defaultValue: '')
  final String emoji;
  @<PERSON>son<PERSON><PERSON>(name: 'is_deleted', defaultValue: 0)
  final int isDeleted;
  @Json<PERSON><PERSON>(name: 'created_at', defaultValue: '')
  final String createdAt;

  const Reaction({
    required this.messageId,
    required this.uid,
    required this.name,
    required this.channelId,
    required this.channelType,
    required this.seq,
    required this.emoji,
    required this.isDeleted,
    required this.createdAt,
  });

  factory Reaction.fromJson(Map<String, dynamic> json) =>
      _$ReactionFromJson(json);

  Map<String, dynamic> toJson() => _$ReactionToJson(this);
}
