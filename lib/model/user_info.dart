import 'package:json_annotation/json_annotation.dart';

part 'user_info.g.dart';

@JsonSerializable()
class UserInfo {
  @Json<PERSON>ey(defaultValue: '')
  final String uid;
  @J<PERSON><PERSON><PERSON>(defaultValue: '')
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String username;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int mute;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int top;
  @<PERSON>son<PERSON><PERSON>(defaultValue: 0)
  final int sex;
  @<PERSON><PERSON><PERSON>ey(defaultValue: '')
  final String category;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'short_no', defaultValue: '')
  final String shortNo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_pwd_on', defaultValue: 0)
  final int chatPwdOn;
  @Json<PERSON>ey(defaultValue: 0)
  final int screenshot;
  @<PERSON>son<PERSON><PERSON>(name: 'revoke_remind', defaultValue: 0)
  final int revokeRemind;
  @<PERSON>son<PERSON><PERSON>(defaultValue: 0)
  final int receipt;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int online;
  @<PERSON>son<PERSON>ey(name: 'last_offline', defaultValue: 0)
  final int lastOffline;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'device_flag', defaultValue: 0)
  final int deviceFlag;
  @JsonKey(defaultValue: 0)
  final int follow;
  @JsonKey(name: 'be_deleted', defaultValue: 0)
  final int beDeleted;
  @JsonKey(name: 'be_blacklist', defaultValue: 0)
  final int beBlacklist;
  @JsonKey(defaultValue: '')
  final String code;
  @JsonKey(defaultValue: '')
  final String vercode;
  @JsonKey(name: 'source_desc', defaultValue: '')
  final String sourceDesc;
  @JsonKey(defaultValue: '')
  final String remark;
  @JsonKey(name: 'is_upload_avatar', defaultValue: 0)
  final int isUploadAvatar;
  @JsonKey(defaultValue: 0)
  final int status;
  @JsonKey(defaultValue: 0)
  final int robot;
  @JsonKey(name: 'is_destroy', defaultValue: 0)
  final int isDestroy;
  @JsonKey(defaultValue: 0)
  final int flame;
  @JsonKey(name: 'flame_second', defaultValue: 0)
  final int flameSecond;
  @JsonKey(name: 'join_group_invite_uid', defaultValue: '')
  final String joinGroupInviteUid;
  @JsonKey(name: 'join_group_invite_name', defaultValue: '')
  final String joinGroupInviteName;
  @JsonKey(name: 'join_group_time', defaultValue: '')
  final String joinGroupTime;
  @JsonKey(defaultValue: '')
  final String signature;
  @JsonKey(name: 'created_at', defaultValue: '')
  final String createdAt;
  @JsonKey(name: 'updated_at', defaultValue: '')
  final String updatedAt;
  @JsonKey(name: 'is_deleted', defaultValue: 0)
  final int isDeleted;
  @JsonKey(defaultValue: 0)
  final int version;
  @JsonKey(defaultValue: '')
  final String email;

  const UserInfo({
    required this.uid,
    required this.name,
    required this.username,
    required this.mute,
    required this.top,
    required this.sex,
    required this.category,
    required this.shortNo,
    required this.chatPwdOn,
    required this.screenshot,
    required this.revokeRemind,
    required this.receipt,
    required this.online,
    required this.lastOffline,
    required this.deviceFlag,
    required this.follow,
    required this.beDeleted,
    required this.beBlacklist,
    required this.code,
    required this.vercode,
    required this.sourceDesc,
    required this.remark,
    required this.isUploadAvatar,
    required this.status,
    required this.robot,
    required this.isDestroy,
    required this.flame,
    required this.flameSecond,
    required this.joinGroupInviteUid,
    required this.joinGroupInviteName,
    required this.joinGroupTime,
    required this.signature,
    required this.createdAt,
    required this.updatedAt,
    required this.isDeleted,
    required this.version,
    required this.email
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}
