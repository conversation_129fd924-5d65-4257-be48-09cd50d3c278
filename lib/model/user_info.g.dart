// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
      uid: json['uid'] as String? ?? '',
      name: json['name'] as String? ?? '',
      username: json['username'] as String? ?? '',
      mute: (json['mute'] as num?)?.toInt() ?? 0,
      top: (json['top'] as num?)?.toInt() ?? 0,
      sex: (json['sex'] as num?)?.toInt() ?? 0,
      category: json['category'] as String? ?? '',
      shortNo: json['short_no'] as String? ?? '',
      chatPwdOn: (json['chat_pwd_on'] as num?)?.toInt() ?? 0,
      screenshot: (json['screenshot'] as num?)?.toInt() ?? 0,
      revokeRemind: (json['revoke_remind'] as num?)?.toInt() ?? 0,
      receipt: (json['receipt'] as num?)?.toInt() ?? 0,
      online: (json['online'] as num?)?.toInt() ?? 0,
      lastOffline: (json['last_offline'] as num?)?.toInt() ?? 0,
      deviceFlag: (json['device_flag'] as num?)?.toInt() ?? 0,
      follow: (json['follow'] as num?)?.toInt() ?? 0,
      beDeleted: (json['be_deleted'] as num?)?.toInt() ?? 0,
      beBlacklist: (json['be_blacklist'] as num?)?.toInt() ?? 0,
      code: json['code'] as String? ?? '',
      vercode: json['vercode'] as String? ?? '',
      sourceDesc: json['source_desc'] as String? ?? '',
      remark: json['remark'] as String? ?? '',
      isUploadAvatar: (json['is_upload_avatar'] as num?)?.toInt() ?? 0,
      status: (json['status'] as num?)?.toInt() ?? 0,
      robot: (json['robot'] as num?)?.toInt() ?? 0,
      isDestroy: (json['is_destroy'] as num?)?.toInt() ?? 0,
      flame: (json['flame'] as num?)?.toInt() ?? 0,
      flameSecond: (json['flame_second'] as num?)?.toInt() ?? 0,
      joinGroupInviteUid: json['join_group_invite_uid'] as String? ?? '',
      joinGroupInviteName: json['join_group_invite_name'] as String? ?? '',
      joinGroupTime: json['join_group_time'] as String? ?? '',
      signature: json['signature'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      isDeleted: (json['is_deleted'] as num?)?.toInt() ?? 0,
      version: (json['version'] as num?)?.toInt() ?? 0,
      email: json['email'] as String? ?? '',
    );

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
      'uid': instance.uid,
      'name': instance.name,
      'username': instance.username,
      'mute': instance.mute,
      'top': instance.top,
      'sex': instance.sex,
      'category': instance.category,
      'short_no': instance.shortNo,
      'chat_pwd_on': instance.chatPwdOn,
      'screenshot': instance.screenshot,
      'revoke_remind': instance.revokeRemind,
      'receipt': instance.receipt,
      'online': instance.online,
      'last_offline': instance.lastOffline,
      'device_flag': instance.deviceFlag,
      'follow': instance.follow,
      'be_deleted': instance.beDeleted,
      'be_blacklist': instance.beBlacklist,
      'code': instance.code,
      'vercode': instance.vercode,
      'source_desc': instance.sourceDesc,
      'remark': instance.remark,
      'is_upload_avatar': instance.isUploadAvatar,
      'status': instance.status,
      'robot': instance.robot,
      'is_destroy': instance.isDestroy,
      'flame': instance.flame,
      'flame_second': instance.flameSecond,
      'join_group_invite_uid': instance.joinGroupInviteUid,
      'join_group_invite_name': instance.joinGroupInviteName,
      'join_group_time': instance.joinGroupTime,
      'signature': instance.signature,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'is_deleted': instance.isDeleted,
      'version': instance.version,
      'email': instance.email,
    };
