import 'dart:convert';

import 'package:and/model/channel_msg_sync.dart';
import 'package:and/model/extension/message_extra.dart';
import 'package:and/model/extension/payload_ext.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension ChannelMsgSyncExtension on ChannelMsgSync {
  WKSyncChannelMsg toWKSyncChannelMsg() {
    WKSyncChannelMsg msg = WKSyncChannelMsg();
    msg.startMessageSeq = startMessageSeq;
    msg.endMessageSeq = endMessageSeq;
    msg.more = more;

    List<WKSyncMsg> msgList = [];
    for (var message in messages) {
      WKSyncMsg syncMsg = WKSyncMsg();
      syncMsg.channelID = message.channelId;
      syncMsg.messageID = message.messageId.toString();
      syncMsg.channelType = message.channelType;
      syncMsg.clientMsgNO = message.clientMsgNo;
      syncMsg.messageSeq = message.messageSeq;
      syncMsg.fromUID = message.fromUid;
      syncMsg.isDeleted = message.isDeleted;
      syncMsg.timestamp = message.timestamp;

      if (message.payload is Map<String, dynamic>) {
        (message.payload as Map<String, dynamic>).fixPayload();
      }
      syncMsg.payload = message.payload;

      syncMsg.messageExtra = message.messageExtra?.toWKSyncExtraMsg();

      msgList.add(syncMsg);
    }

    msg.messages = msgList;

    return msg;
  }
}
