import 'package:and/model/sync_msg.dart';
import 'package:and/model/sync_reaction.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension SyncReactionExtension on SyncReaction {
  WKSyncMsgReaction toWKSyncMsgReaction() {
    WKSyncMsgReaction msg = WKSyncMsgReaction();
    msg.messageID = messageId.toString();
    msg.uid = uid;
    msg.name = name;
    msg.channelID = channelId;
    msg.channelType = channelType;
    msg.seq = seq;
    msg.emoji = emoji;
    msg.isDeleted = isDeleted;
    msg.createdAt = createdAt;
    return msg;
  }
}
