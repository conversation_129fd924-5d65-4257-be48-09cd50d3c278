import 'package:and/model/sync_msg.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension SyncMsgExtension on SyncMsg {
  WKSyncMsg toWKSyncMsg() {
    WKSyncMsg msg = WKSyncMsg();
    msg.messageID = messageId.toString();
    msg.messageSeq = messageSeq;
    msg.clientMsgNO = clientMsgNo;
    msg.fromUID = fromUid;
    msg.channelID = channelId;
    msg.channelType = channelType;
    msg.voiceStatus = voiceStatus;
    msg.timestamp = timestamp;
    msg.isDeleted = isDelete;
    msg.readedCount = readedCount;
    msg.unreadCount = unreadCount;
    msg.extraVersion = extraVersion;
    msg.payload = payload;
    return msg;
  }
}
