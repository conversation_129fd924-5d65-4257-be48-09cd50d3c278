
import 'package:and/model/extension/message_extra.dart';
import 'package:and/model/extension/payload_ext.dart';
import 'package:and/model/message.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension MessageExtension on Message {
  WKSyncMsg toWKSyncMsg() {
    WKSyncMsg msg = WKSyncMsg();
    msg.channelID = channelId;
    msg.messageID = messageId.toString();
    msg.channelType = channelType;
    msg.clientMsgNO = clientMsgNo;
    msg.messageSeq = messageSeq;
    msg.fromUID = fromUid;
    msg.isDeleted = isDeleted;
    msg.timestamp = timestamp;

    try {
      if (payload is Map<String, dynamic>) {
        (payload as Map<String, dynamic>).fixPayload();
      }
      msg.payload = payload;
      // msg.payload = jsonDecode(utf8.decode(base64Decode(payload)));
    } catch (e) {
      print('异常了');
    }
    // 解析扩展
    var extraJson = messageExtra;
    if (extraJson != null) {
      var extra = extraJson.toWKSyncExtraMsg();
      msg.messageExtra = extra;
    }
    return msg;
  }

  void fixPayload(Map<String, dynamic> payload) {
    // 解析 JSON 字符串为 Map
    if (payload['timeTrad'] is String) {
      payload['timeTrad'] = int.tryParse(payload['timeTrad']) ?? 0;
    }
  }
}
