// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reaction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Reaction _$ReactionFromJson(Map<String, dynamic> json) => Reaction(
      messageId: json['message_id'] as String? ?? '',
      uid: json['uid'] as String? ?? '',
      name: json['name'] as String? ?? '',
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      seq: (json['seq'] as num?)?.toInt() ?? 0,
      emoji: json['emoji'] as String? ?? '',
      isDeleted: (json['is_deleted'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] as String? ?? '',
    );

Map<String, dynamic> _$ReactionToJson(Reaction instance) => <String, dynamic>{
      'message_id': instance.messageId,
      'uid': instance.uid,
      'name': instance.name,
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'seq': instance.seq,
      'emoji': instance.emoji,
      'is_deleted': instance.isDeleted,
      'created_at': instance.createdAt,
    };
