import 'package:json_annotation/json_annotation.dart';

part 'sync_msg.g.dart';

@JsonSerializable()
class SyncMsg {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_id', defaultValue: 0)
  final int messageId;
  @<PERSON>son<PERSON><PERSON>(name: 'message_seq', defaultValue: 0)
  final int messageSeq;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'client_msg_no', defaultValue: '')
  final String clientMsgNo;
  @<PERSON>son<PERSON>ey(name: 'from_uid', defaultValue: '')
  final String fromUid;
  @<PERSON>son<PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'voice_status', defaultValue: 0)
  final int voiceStatus;
  @<PERSON>sonKey(name: 'timestamp', defaultValue: 0)
  final int timestamp;
  @<PERSON>son<PERSON>ey(name: 'is_delete', defaultValue: 0)
  final int isDelete;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'unread_count', defaultValue: 0)
  final int unreadCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'readed_count', defaultValue: 0)
  final int readedCount;
  @<PERSON>son<PERSON><PERSON>(name: 'extra_version', defaultValue: 0)
  final int extraVersion;
  final dynamic payload;
  final SyncMsgHeader? header;

  const SyncMsg({
    required this.messageId,
    required this.messageSeq,
    required this.clientMsgNo,
    required this.fromUid,
    required this.channelId,
    required this.channelType,
    required this.voiceStatus,
    required this.timestamp,
    required this.isDelete,
    required this.unreadCount,
    required this.readedCount,
    required this.extraVersion,
    required this.payload,
    this.header,
  });

  factory SyncMsg.fromJson(Map<String, dynamic> json) =>
      _$SyncMsgFromJson(json);

  Map<String, dynamic> toJson() => _$SyncMsgToJson(this);
}

@JsonSerializable()
class SyncMsgHeader {
  @JsonKey(name: 'red_dot', defaultValue: 0)
  final int redDot;
  @JsonKey(name: 'no_persist', defaultValue: 0)
  final int noPersist;
  @JsonKey(name: 'sync_once', defaultValue: 0)
  final int syncOnce;

  const SyncMsgHeader({
    required this.redDot,
    required this.noPersist,
    required this.syncOnce,
  });

  factory SyncMsgHeader.fromJson(Map<String, dynamic> json) =>
      _$SyncMsgHeaderFromJson(json);

  Map<String, dynamic> toJson() => _$SyncMsgHeaderToJson(this);
}
