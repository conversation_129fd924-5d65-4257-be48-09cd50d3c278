import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class GlobalSearchResultModel {
  GlobalSearchViewType viewType; // 视图类型
  GlobalSearchType searchType; // 搜索类型
  String? keyword; // 搜索关键字
  String? content; // 内容
  int messageCount; // 聊天记录数量
  WKChannel? channel; // 频道信息
  WKMsg? msg; // 频道消息

  GlobalSearchResultModel({
    this.viewType = GlobalSearchViewType.header,
    this.searchType = GlobalSearchType.all,
    this.keyword,
    this.content,
    this.messageCount = 0,
    this.channel,
    this.msg,
  });
}
