import 'dart:math';

import 'package:and/app.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_ui_conversation_msg_ext.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/module/search/globalSearch/model/global_search_view_model.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/db/message.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

/// 全局搜索类型
enum GlobalSearchType {
  all, // 搜索所有
  contacts, // 搜索联系人
  groups, // 搜索群聊
  conversation, // 搜索聊天记录
  channelMsg, // 搜索频道消息(搜索具体跟某个人/群的聊天记录),
  recentConversation, // 搜索最近联系人
}

/// 全局搜索视图类型
enum GlobalSearchViewType {
  header, // 头部
  contact, // 联系人
  group, // 群
  conversation, // 全局搜索聊天记录
  channelMsg, // 搜索频道消息(搜索具体跟某个人/群的聊天记录)
  footer // 尾部 有内容显示内容（查看更多xxx）没内容显示空白间隙
}

const int globalSearchMaxCount = 4;

class GlobalSearchLogic extends GetxController {
  final String key = "ui_global_search";

  var results = RxList<GlobalSearchResultModel>();
  final selectedChannels = <WKChannel>[].obs;

  String searchKey = '';
  GlobalSearchType searchType = GlobalSearchType.all;
  WKChannel? channel;

  GlobalSearchLogic(GlobalSearchArgument argument) {
    if (argument.searchType != null) {
      searchType = argument.searchType!;
    }
    if (argument.channel != null) {
      channel = argument.channel!;
    }
    if (argument.searchKey != null) {
      searchKey = argument.searchKey ?? '';
    }
  }

  @override
  void onReady() {
    super.onReady();
    _initListener();
    if (searchKey.isNotEmpty ||
        searchType == GlobalSearchType.recentConversation) {
      startSearch(searchKey);
    }
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  Future<void> startSearch(String key) async {
    EasyLoadingHelper.show(onAction: () async {
      var list = await _search(key);
      results.value = list ?? [];
    });
  }

  Future<List<GlobalSearchResultModel>?> _search(String key) async {
    if (key.isEmpty) {
      if (searchType != GlobalSearchType.recentConversation) {
        searchKey = '';
        return [];
      }
    }
    searchKey = key;
    List<GlobalSearchResultModel>? items = [];
    switch (searchType) {
      case GlobalSearchType.contacts:
        items = await searchContacts(searchKey);
        break;
      case GlobalSearchType.conversation:
        items = await searchMessage(searchKey);
        break;
      case GlobalSearchType.groups:
        items = await searchGroup(searchKey);
        break;
      case GlobalSearchType.channelMsg:
        items = await searchChannelMsgs(searchKey);
        break;
      case GlobalSearchType.recentConversation:
        if (searchKey.isEmpty) {
          items = await searchRecentChannelMsgs(searchKey);
        } else {
          items = await searchAll(searchKey);
        }
        break;
      case GlobalSearchType.all:
        items = await searchAll(searchKey);
        break;
    }

    return items;
  }

  Future<List<GlobalSearchResultModel>?> searchAll(String keyword) async {
    List<GlobalSearchResultModel> items = [];
    final contacts = await searchContacts(searchKey);
    if (contacts != null) {
      items.addAll(contacts);
    }
    final groups = await searchGroup(searchKey);
    if (groups != null && groups.isNotEmpty) {
      items.addAll(groups);
    }
    final conversations = await searchMessage(searchKey);
    if (conversations != null && conversations.isNotEmpty) {
      items.addAll(conversations);
    }
    return items;
  }

  /// 搜索联系人
  Future<List<GlobalSearchResultModel>?> searchContacts(String keyword) async {
    final limit =
        searchType == GlobalSearchType.all ? globalSearchMaxCount : 1000;
    List<WKChannel> channels = await WKIM.shared.channelManager
        .searchWithChannelTypeAndFollow(searchKey, WKChannelType.personal, 1);
    if (channels.isEmpty) {
      return null;
    }
    List<GlobalSearchResultModel> items = [];
    final header = GlobalSearchResultModel(
      viewType: GlobalSearchViewType.header,
      searchType: GlobalSearchType.contacts,
      content: globalContext?.l10n.searchContact,
    );

    items.add(header);
    for (int i = 0; i < min(limit, channels.length); i++) {
      final channelInfo = channels[i];
      final contact = GlobalSearchResultModel(
          viewType: GlobalSearchViewType.contact,
          searchType: GlobalSearchType.contacts,
          messageCount: channels.length,
          keyword: searchKey,
          channel: channelInfo);

      items.add(contact);
    }

    if (channels.length > limit) {
      final more = GlobalSearchResultModel(
          viewType: GlobalSearchViewType.footer,
          searchType: GlobalSearchType.contacts,
          content: globalContext?.l10n.searchMoreContacts);

      items.add(more);
    }

    final footer = GlobalSearchResultModel(
      viewType: GlobalSearchViewType.footer,
      searchType: GlobalSearchType.contacts,
    );

    items.add(footer);

    return items;
  }

  /// 搜索群聊
  Future<List<GlobalSearchResultModel>?> searchGroup(String keyword) async {
    final limit =
        searchType == GlobalSearchType.all ? globalSearchMaxCount : 1000;
    List<WKChannelSearchResult> result =
        await WKIM.shared.channelManager.search(searchKey);
    if (result.isEmpty) {
      return null;
    }
    List<GlobalSearchResultModel> items = [];
    final header = GlobalSearchResultModel(
      viewType: GlobalSearchViewType.header,
      searchType: GlobalSearchType.groups,
      content: globalContext?.l10n.searchGroup,
    );

    items.add(header);
    for (int i = 0; i < min(limit, result.length); i++) {
      final channelInfo = result[i].channel;
      if (channelInfo != null) {
        final group = GlobalSearchResultModel(
            viewType: GlobalSearchViewType.group,
            searchType: GlobalSearchType.groups,
            messageCount: result.length,
            keyword: searchKey,
            channel: channelInfo);
        items.add(group);
      }
    }

    if (result.length > limit) {
      final more = GlobalSearchResultModel(
        viewType: GlobalSearchViewType.footer,
        searchType: GlobalSearchType.groups,
        content: globalContext?.l10n.searchMoreGroups,
        keyword: searchKey,
      );
      items.add(more);
    }

    final footer = GlobalSearchResultModel(
      viewType: GlobalSearchViewType.footer,
      searchType: GlobalSearchType.groups,
    );

    items.add(footer);

    return items;
  }

  /// 搜索消息
  Future<List<GlobalSearchResultModel>?> searchMessage(String keyword) async {
    final limit =
        searchType == GlobalSearchType.all ? globalSearchMaxCount : 1000;
    List<WKMessageSearchResult> result =
        await WKIM.shared.messageManager.search(searchKey);
    if (result.isEmpty) {
      return null;
    }
    List<GlobalSearchResultModel> items = [];
    final header = GlobalSearchResultModel(
      viewType: GlobalSearchViewType.header,
      searchType: GlobalSearchType.conversation,
      content: globalContext?.l10n.searchMessage,
      keyword: searchKey,
    );

    items.add(header);
    for (int i = 0; i < min(limit, result.length); i++) {
      if (result[i].channel != null) {
        String? content;
        WKMsg? msg;
        if (result[i].messageCount == 1) {
          content = result[i].searchableWord;
          msg = await MessageDB.shared.queryWithClientSeq(result[i].clientSeq);
        } else {
          content =
              globalContext?.l10n.searchMessageCount(result[i].messageCount);
        }
        final data = GlobalSearchResultModel(
            viewType: GlobalSearchViewType.conversation,
            searchType: GlobalSearchType.conversation,
            content: content,
            messageCount: result[i].messageCount,
            keyword: searchKey,
            channel: result[i].channel,
            msg: msg);

        items.add(data);
      }
    }

    if (result.length > limit) {
      final more = GlobalSearchResultModel(
        viewType: GlobalSearchViewType.footer,
        searchType: GlobalSearchType.conversation,
        keyword: searchKey,
        content: globalContext?.l10n.searchMoreMessage,
      );
      items.add(more);
    }

    return items;
  }

  /// 搜索频道消息(搜索具体跟某个人/群的聊天记录)
  Future<List<GlobalSearchResultModel>?> searchChannelMsgs(
      String keyword) async {
    List<WKMsg> result = await WKIM.shared.messageManager.searchWithChannel(
        keyword,
        channel?.channelID ?? '',
        channel?.channelType ?? WKChannelType.personal);
    if (result.isEmpty) {
      return null;
    }
    List<GlobalSearchResultModel> items = [];
    for (int i = 0; i < result.length; i++) {
      final data = GlobalSearchResultModel(
        viewType: GlobalSearchViewType.channelMsg,
        searchType: GlobalSearchType.channelMsg,
        keyword: keyword,
        msg: result[i],
      );
      items.add(data);
    }

    return items;
  }

  /// 最近聊天记录
  Future<List<GlobalSearchResultModel>?> searchRecentChannelMsgs(
      String keyword) async {
    var result = await WKIM.shared.conversationManager.getAll();
    await result.sortMsg();

    if (result.isEmpty) {
      return null;
    }
    List<GlobalSearchResultModel> items = [];
    final header = GlobalSearchResultModel(
      viewType: GlobalSearchViewType.header,
      searchType: GlobalSearchType.conversation,
      content: globalContext?.l10n.searchRecent,
      keyword: searchKey,
    );

    items.add(header);
    for (int i = 0; i < result.length; i++) {
      final data = GlobalSearchResultModel(
        viewType: GlobalSearchViewType.contact,
        searchType: GlobalSearchType.recentConversation,
        keyword: keyword,
        channel: WKChannel(result[i].channelID, result[i].channelType),
      );
      items.add(data);
    }
    return items;
  }

  bool select(GlobalSearchResultModel item) {
    bool isSelected =
        !selectedChannels.any((e) => e.channelID == item.channel?.channelID);
    if (isSelected) {
      var channel = item.channel;
      if (channel != null) {
        selectedChannels.add(channel);
      }
      results.refresh();
      return true;
    } else {
      selectedChannels
          .removeWhere((e) => e.channelID == item.channel?.channelID);
      results.refresh();
      return false;
    }
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      var msg = results.firstWhereOrNull((msg) =>
          msg.channel?.channelID == channel.channelID &&
          msg.channel?.channelType == channel.channelType);
      if (msg != null) {
        msg.channel = channel;
      }
      results.refresh();
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
  }
}
