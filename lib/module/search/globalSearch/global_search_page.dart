import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/constant/wk_channel_status.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/model/global_search_view_model.dart';
import 'package:and/module/search/globalSearch/widget/search_channel_message_item.dart';
import 'package:and/module/search/globalSearch/widget/search_contact_item.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'widget/search_conversation_item.dart';

class GlobalSearchArgument {
  String? searchKey;
  WKChannel? channel;
  GlobalSearchType? searchType = GlobalSearchType.all;
  bool isMultiSelect;
  String? title;
  Function(List<WKChannel> channels)? onSelectedChannel;
  int? uniqueID;

  GlobalSearchArgument(
      {this.searchKey,
      this.channel,
      this.title,
      this.searchType,
      this.isMultiSelect = false,
      this.onSelectedChannel}) {
    uniqueID = DateTime.now().millisecondsSinceEpoch;
  }

  factory GlobalSearchArgument.fromGet() {
    return Get.arguments as GlobalSearchArgument;
  }

  String getTag() {
    return "$uniqueID";
  }
}

class GlobalSearchPage extends StatefulWidget {
  const GlobalSearchPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _GlobalSearchPageState();
  }

  static Future<void> open(
      {String? searchKey,
      WKChannel? channel,
      GlobalSearchType? searchType = GlobalSearchType.all,
      String? title,
      bool isMultiSelect = false,
      Function(List<WKChannel> channels)? onSelectedChannel}) async {
    await Get.toNamed(RouteGet.globalSearch,
        preventDuplicates: false,
        arguments: GlobalSearchArgument(
            searchKey: searchKey,
            channel: channel,
            searchType: searchType,
            title: title,
            isMultiSelect: isMultiSelect,
            onSelectedChannel: onSelectedChannel));
  }
}

class _GlobalSearchPageState extends State<GlobalSearchPage> {
  late GlobalSearchArgument argument = GlobalSearchArgument.fromGet();
  late GlobalSearchLogic logic =
      Get.find<GlobalSearchLogic>(tag: argument.getTag());
  late RxList<GlobalSearchResultModel> results = logic.results;
  late RxList<WKChannel> selectedItems = logic.selectedChannels;

  final TextEditingController _searchController = TextEditingController();

  void _onTextChanged(String text) {
    if (argument.searchType == GlobalSearchType.channelMsg) {
      return;
    }
    logic.startSearch(text);
  }

  void _onSubmitted(String text) {
    if (text.isEmpty) {
      _hideKeyboard();
      return;
    }
    logic.startSearch(text);
  }

  void clearSearch() {
    _searchController.clear();
    logic.startSearch('');
  }

  void _hideKeyboard() {
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    var hasTitle = argument.title?.isNotEmpty ?? false;
    return GestureDetector(
      onTap: _hideKeyboard,
      child: Scaffold(
        appBar: AppBar(
          titleSpacing: 0,
          title: hasTitle ? Text(argument.title ?? '') : _buildSearchBar(),
          actions: argument.isMultiSelect ? [Obx(() => _submitButton())] : null,
        ),
        body: Column(
          children: [
            if (hasTitle)
              Container(
                margin: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: _buildSearchBar(),
              ),
            Expanded(child: Obx(() => _buildSearchResult())),
          ],
        ),
      ),
    );
  }

  Widget _submitButton() {
    var selectedCount = selectedItems.length;
    return Padding(
      padding: EdgeInsets.only(right: 15),
      child: SubmitButton(
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          enable: selectedCount > 0,
          onPressed: () {
            if (argument.onSelectedChannel != null) {
              argument.onSelectedChannel!(logic.selectedChannels);
            } else {
              Get.back(result: logic.selectedChannels);
            }
          },
          text: "${context.l10n.globalConfirm}($selectedCount)"),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 36,
      margin: EdgeInsets.only(right: 16), // 添加右侧 12px 间距
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: _searchController,
        autofocus: true,
        onChanged: _onTextChanged,
        onSubmitted: _onSubmitted,
        decoration: InputDecoration(
          hintText: context.l10n.globalSearch,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: clearSearch,
                )
              : null,
        ),
      ),
    );
  }

  Future<bool> canSelected(GlobalSearchResultModel model) async {
    if (model.channel == null) {
      return true;
    }

    var channel = await WKIM.shared.channelManager
        .getChannel(model.channel!.channelID, model.channel!.channelType);
    if (channel == null) {
      return true;
    }

    var isForbidden = false;
    if (channel.channelType == WKChannelType.group) {
      var channelMember = await WKIM.shared.channelMemberManager.getMember(
          channel.channelID, channel.channelType, CacheHelper.uid ?? '');
      if (channel.forbidden == 1) {
        if (channelMember != null) {
          isForbidden = channelMember.role == WKChannelMemberRole.normal;
        }
      } else {
        if (channelMember != null) {
          isForbidden = channelMember.forbiddenExpirationTime > 0;
        } else {
          isForbidden = false;
        }
      }
    }
    var isBan = channel.status == WkChannelStatus.statusDisabled;
    if (isForbidden || isBan) {
      return false;
    }
    return true;
  }

  Widget _buildSearchResult() {
    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        var item = results[index];
        var isSelected =
            selectedItems.any((e) => e.channelID == item.channel?.channelID);
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: argument.isMultiSelect ? () {} : null,
          child: Container(
              color: Colors.white,
              child: FutureBuilder<bool>(
                future: canSelected(item),
                builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
                  var canSelected = snapshot.data ?? true;
                  Function()? onItemTap;
                  Function(WKChannel)? onSelectChannelTap;
                  if (canSelected) {
                    if (argument.isMultiSelect) {
                      onItemTap = () {
                        logic.select(item);
                      };
                    }

                    if (argument.onSelectedChannel != null) {
                      onSelectChannelTap = (channel) {
                        if (item.channel != null) {
                          argument.onSelectedChannel?.call([channel]);
                        }
                      };
                    }
                  }
                  return Row(
                    children: [
                      if (argument.isMultiSelect &&
                          item.viewType != GlobalSearchViewType.header &&
                          item.viewType != GlobalSearchViewType.footer)
                        Container(
                          width: 40,
                          height: 40,
                          alignment: Alignment.center,
                          child: Checkbox(
                            value: isSelected,
                            onChanged: canSelected
                                ? (value) {
                                    onItemTap?.call();
                                  }
                                : null,
                          ),
                        ),
                      Expanded(
                          child:
                              _buildSearchResultItem(item, onSelectChannelTap)),
                    ],
                  );
                },
              )),
        );
      },
    );
  }

  Widget _buildSearchResultItem(
      GlobalSearchResultModel item, Function(WKChannel)? onItemTap) {
    switch (item.viewType) {
      case GlobalSearchViewType.header:
        return Container(
          height: 30,
          padding: EdgeInsets.symmetric(horizontal: 12),
          alignment: Alignment.centerLeft,
          color: Colors.white,
          child: Text(item.content ?? '',
              style:
                  TextStyle(color: Colors.grey, fontWeight: FontWeight.bold)),
        );
      case GlobalSearchViewType.contact:
      case GlobalSearchViewType.group:
        if (item.channel != null) {
          return UiSearchContactItem(
              channel: item.channel!, keyword: item.keyword, onTap: onItemTap);
        }
        return Container();
      case GlobalSearchViewType.conversation:
        if (item.channel != null) {
          return UiSearchConversationItem(
            channel: item.channel!,
            msg: item.msg,
            content: item.content,
            keyword: item.keyword,
            messageCount: item.messageCount,
            onTap: onItemTap,
          );
        }
        return Container();
      case GlobalSearchViewType.channelMsg:
        if (item.msg != null) {
          return UiSearchMessageItem(
            msg: item.msg!,
            keyword: item.keyword,
            searchType: item.searchType,
            onTap: onItemTap,
          );
        }
        return Container();
      case GlobalSearchViewType.footer:
        if (item.content != null && item.content!.isNotEmpty) {
          return Container(
            height: 30,
            padding: EdgeInsets.symmetric(horizontal: 20),
            color: Colors.white,
            alignment: Alignment.centerLeft,
            child: GestureDetector(
                onTap: () {
                  GlobalSearchPage.open(
                      searchKey: item.keyword, searchType: item.searchType);
                },
                child: Row(
                  children: [
                    Image.asset(ImagePath.ic_search_more,
                        width: 18, height: 18, fit: BoxFit.contain),
                    SizedBox(width: 8),
                    Text(item.content ?? '',
                        style: const TextStyle(
                            color: DColor.color5A7095, fontSize: 14))
                  ],
                )),
          );
        }
        return SizedBox(height: 15);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();

    EasyLoading.dismiss();
    Get.delete<GlobalSearchLogic>(tag: argument.getTag());
  }
}
