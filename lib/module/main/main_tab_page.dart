import 'dart:async';

import 'package:and/bloc/navigation/navi_count_bloc.dart';
import 'package:and/bloc/navigation/navi_count_state.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/common/theme/app_theme.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/appupgrade/app_upgrade_utils.dart';
import 'package:and/module/contact/home_contact_page.dart';
import 'package:and/module/conversation/home_conversation_page.dart';
import 'package:and/module/main/main_tab_logic.dart';
import 'package:and/module/my/my_page.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/im/background_task.dart';
import 'package:and/utils/network_utils.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:and/utils/svg_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/wkim.dart';

typedef PageBuilder = Widget? Function(BuildContext context,
    {required int pageIndex, required int currentIndex});

enum TabType { conversations, contacts, my }

class PageNavigationBarItem {
  final TabType tabType;
  final Widget? icon;
  final Widget? activeIcon;
  final Function(BuildContext) title;

  final PageBuilder pageBuilder;

  PageNavigationBarItem(
      {required this.tabType,
      this.icon,
      this.activeIcon,
      required this.title,
      required this.pageBuilder});

  PageNavigationBarItem.image(
      {required this.tabType,
      required String image,
      required String activeImage,
      required this.title,
      required this.pageBuilder})
      : icon = Image.asset(image),
        activeIcon = Image.asset(activeImage);

  PageNavigationBarItem.svg(
      {required this.tabType,
      required String image,
      required String activeImage,
      required this.title,
      required this.pageBuilder})
      : icon = SvgPicture.asset(image),
        activeIcon = SvgPicture.asset(activeImage);

  PageNavigationBarItem.icon(
      {required this.tabType,
      required IconData image,
      required IconData activeImage,
      required this.title,
      required this.pageBuilder})
      : icon = Icon(image),
        activeIcon = Icon(activeImage);
}

class MainTabPage extends StatefulWidget {
  const MainTabPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MainTabPageState();
  }
}

class _MainTabPageState extends State<MainTabPage> {
  final logic = Get.put<MainTabLogic>(MainTabLogic());
  late final AppLifecycleListener _lifecycleListener;
  final PageController _pageController = PageController();
  int _selectedIndex = 0;

  final List<PageNavigationBarItem> _items = <PageNavigationBarItem>[
    PageNavigationBarItem.svg(
        tabType: TabType.conversations,
        image: SvgPath.ic_chat_n,
        activeImage: SvgPath.ic_chat_s,
        title: (context) {
          return context.l10n.tabbarConversations;
        },
        pageBuilder: (context, {int? pageIndex, int? currentIndex}) {
          return HomeConversationPage();
        }),
    PageNavigationBarItem.svg(
        tabType: TabType.contacts,
        image: SvgPath.ic_contacts_n,
        activeImage: SvgPath.ic_contacts_s,
        title: (context) {
          return context.l10n.tabbarContacts;
        },
        pageBuilder: (context, {int? pageIndex, int? currentIndex}) =>
            HomeContactPage()),
    PageNavigationBarItem.svg(
        tabType: TabType.my,
        image: SvgPath.ic_mine_n,
        activeImage: SvgPath.ic_mine_s,
        title: (context) {
          return context.l10n.tabbarMine;
        },
        pageBuilder: (context, {int? pageIndex, int? currentIndex}) =>
            MyPage()),
  ];

  @override
  void initState() {
    super.initState();
    _lifecycleListener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );
    _onStateChanged(AppLifecycleState.resumed);

    AppUpgradeUtils.checkAppUpdateHome(context);

    NotificationUtils.requestNotificationPermission();
  }

  @override
  void dispose() {
    _lifecycleListener.dispose();
    WKIM.shared.connectionManager.removeOnConnectionStatus("main");
    NetworkManager.instance.stopListening();
    super.dispose();
  }

  void _onStateChanged(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      BackgroundTask.endBackgroundTask();
      
      // 应用从后台进入前台时，立即恢复socket连接
      WKIM.shared.connectionManager.resumeConnection();

      if (CacheHelper.syncFriend) {
        await HttpUtils.syncFriends();
      }
      HttpUtils.syncFriendApplyCount();
      HttpUtils.syncOnlineUsers();
    } else if (state == AppLifecycleState.paused) {
      // WKIM.shared.connectionManager.disconnect(false);
      BackgroundTask.startBackgroundTask();
      // 退到后台时清除角标
      NotificationUtils.updateBadge();
    }
  }

  @override
  Widget build(BuildContext context) {
    final textScaler = MediaQuery.of(context).textScaler;
    return Scaffold(
        body: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemBuilder: (ctx, index) {
                  PageNavigationBarItem item = _items[index];
                  return item.pageBuilder(context,
                      pageIndex: index, currentIndex: _selectedIndex);
                },
                itemCount: _items.length,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() => _selectedIndex = index);
                },
              ),
            ),
          ],
        ),
        bottomNavigationBar: _items.length > 1
            ? Theme(
                data: AppTheme.lightTheme.copyWith(
                  // 去掉水波纹效果
                  splashColor: Colors.transparent,
                  // 去掉长按效果
                  // highlightColor: Colors.transparent,
                ),
                child: BottomNavigationBar(
                    backgroundColor: Colors.white,
                    elevation: 10,
                    type: BottomNavigationBarType.fixed,
                    currentIndex: _selectedIndex,
                    selectedItemColor: DColor.primaryColor,
                    unselectedItemColor: DColor.primaryColor,
                    selectedLabelStyle: TextStyle(
                      fontSize: textScaler.scale(14),
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontSize: textScaler.scale(12),
                    ),
                    items: _items
                        .map((item) => BottomNavigationBarItem(
                              icon:
                                  _buildIcon(context, item.tabType, item.icon),
                              activeIcon: _buildIcon(context, item.tabType,
                                  item.activeIcon ?? item.icon,
                                  isActive: true),
                              label: item.title(context),
                            ))
                        .toList(),
                    onTap: (index) async {
                      await jump(index);
                    }),
              )
            : null);
  }

  Future<void> jump(int index) async {
    if (_selectedIndex == index) {
      return;
    }
    _pageController.jumpToPage(index);
    setState(() {
      _selectedIndex = index;
    });
  }

  Widget _buildIcon(BuildContext context, TabType type, Widget? iconWidget,
      {bool isActive = false}) {
    final textScaler = MediaQuery.of(context).textScaler;
    return BlocBuilder<NaviCountBloc, NaviCountState>(
      builder: (context, state) {
        var count = state.counts[type] ?? 0;
        return Badge(
          isLabelVisible: count > 0,
          backgroundColor: Colors.red,
          textStyle: TextStyles.fontSize13Normal
              .copyWith(fontSize: textScaler.scale(8), color: Colors.white),
          label: Text(FormatUtils.formatUnreadCount(count)),
          child: iconWidget != null
              ? SizedBox(
                  height: textScaler.scale(18),
                  child: FittedBox(
                    fit: BoxFit.contain,
                    child: iconWidget,
                  ))
              : Container(),
        );
      },
    );
  }
}
