import 'dart:io';

import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/user_info_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/invite_code.dart';
import 'package:and/model/user_login_info.dart';
import 'package:and/module/common/controller/simple_controller.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_utils.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class MyInfoLogic extends SimpleController<UserLoginInfo> {
  Rx<InviteInfo?> inviteInfo = Rx<InviteInfo?>(CacheHelper.inviteInfo);

  MyInfoLogic();

  @override
  void onReady() {
    super.onReady();
    data.value = CacheHelper.userProfile;
    refreshData();
  }

  @override
  Future<UserLoginInfo?> loadData() async {
    var invite = await HttpUtils.getInviteCode();
    if (invite != null) {
      inviteInfo.value = invite;
      CacheHelper.saveInviteInfo(invite);
    }
    return CacheHelper.userProfile;
  }

  /// 修改名字
  void reName(BuildContext context, String? name) async {
    String? result = await DialogUtils.showInputDialog(context,
        title: context.l10n.name, defaultValue: name ??= context.l10n.name);
    if (result != null) {
      EasyLoadingHelper.show(onAction: () async {
        var applyResult =
            await HttpUtils.updateInfo(UserInfoKeys.name, result) ?? false;
        if (applyResult) {
          var loginInfo = CacheHelper.userProfile;
          if (loginInfo == null) return;
          var newInfo = loginInfo.copyWith(name: result);
          CacheHelper.saveUserProfile(newInfo);
          data.value = newInfo;
          HttpUtils.getChannel(newInfo.uid ?? '', WKChannelType.personal);
          showToast(context.l10n.modifySuccess);
        } else {
          showToast(context.l10n.modifyFail);
        }
      });
    }
  }

  /// 修改签名
  void modifySignature(BuildContext context, String? signature) async {
    String? result = await DialogUtils.showInputDialog(context,
        title: context.l10n.signature,
        hint: context.l10n.signatureLimit,
        defaultValue: signature ??= context.l10n.signature);
    if (result != null) {
      EasyLoadingHelper.show(onAction: () async {
        var applyResult =
            await HttpUtils.updateInfo(UserInfoKeys.signature, result) ?? false;
        if (applyResult) {
          var loginInfo = CacheHelper.userProfile;
          if (loginInfo == null) return;
          var newInfo = loginInfo.copyWith(signature: result);
          CacheHelper.saveUserProfile(newInfo);
          data.value = newInfo;
          HttpUtils.getChannel(newInfo.uid ?? '', WKChannelType.personal);
          showToast(context.l10n.modifySuccess);
        } else {
          showToast(context.l10n.modifyFail);
        }
      });
    }
  }

  /// 修改Gleezy号
  void modifyShortNo(BuildContext context, String? shortNo) async {
    String? result = await DialogUtils.showInputDialog(context,
        title: context.l10n.shortNo(context.l10n.appName),
        hint: context.l10n.shortNoRule,
        defaultValue: shortNo ?? "");
    if (result != null) {
      EasyLoadingHelper.show(onAction: () async {
        var applyResult =
            await HttpUtils.updateInfo(UserInfoKeys.shortNo, result) ?? false;
        if (applyResult) {
          var loginInfo = CacheHelper.userProfile;
          if (loginInfo == null) return;
          var newInfo = loginInfo.copyWith(shortNo: result);
          CacheHelper.saveUserProfile(newInfo);
          data.value = newInfo;
          HttpUtils.getChannel(newInfo.uid ?? '', WKChannelType.personal);
          showToast(context.l10n.modifySuccess);
        } else {
          showToast(context.l10n.modifyFail);
        }
      });
    }
  }

  /// 修改邀请码 只能修改一次 格式：4-8位字母/数字
  void modifyInviteCode(BuildContext context, String? code) async {
    String? result = await DialogUtils.showInputDialog(context,
        title: context.l10n.inviteCode,
        hint: context.l10n.inviteNoRule,
        defaultValue: code ??= context.l10n.inviteCode);
    if (result != null) {
      EasyLoadingHelper.show(onAction: () async {
        var response =
            await HttpUtils.modifyInviteCode(UserInfoKeys.inviteCode, result);
        if (response != null) {
          CacheHelper.saveInviteInfo(response);
          inviteInfo.value = response;
          data.value = CacheHelper.userProfile;
          showToast(context.l10n.modifySuccess);
        } else {
          showToast(context.l10n.modifyFail);
        }
      });
    }
  }

  /// 修改性别
  void updateGender(BuildContext context, int gender) async {
    EasyLoadingHelper.show(onAction: () async {
      var applyResult =
          await HttpUtils.updateInfo(UserInfoKeys.sex, '$gender') ?? false;
      if (applyResult) {
        var loginInfo = CacheHelper.userProfile;
        if (loginInfo == null) return;
        var newInfo = loginInfo.copyWith(sex: gender);
        CacheHelper.saveUserProfile(newInfo);
        data.value = newInfo;
        HttpUtils.getChannel(newInfo.uid ?? '', WKChannelType.personal);
        showToast(context.l10n.modifySuccess);
      } else {
        showToast(context.l10n.modifyFail);
      }
    });
  }

  /// 上传头像
  Future<bool> uploadPhoto(String filePath) async {
    var result = await HttpUtils.uploadAvatar(data.value?.uid ?? "", filePath);
    if (result) {
      // 删除头像本地缓存
      CachedNetworkImage.evictFromCache(CommonHelper.getMyAvatarUrl());

      var loginInfo = CacheHelper.userProfile;
      data.value = loginInfo;
      HttpUtils.getChannel(loginInfo?.uid ?? '', WKChannelType.personal);
      return true;
    } else {
      return false;
    }
  }

  /// 对图片进行处理：裁剪->压缩->上传
  Future<File?> imageProcessing(BuildContext context, File pickedFile) async {
    // 裁剪
    var imageFile = await ImageUtils.cropImage(context,
        file: pickedFile,
        aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1),
        lockAspectRatio: false);
    if (imageFile != null) {
      EasyLoading.show();
      // 压缩图片
      imageFile = await ImageUtils.compressImage2(imageFile);
      bool uploadSuccess = await uploadPhoto(imageFile.path);
      EasyLoading.dismiss();
      if (uploadSuccess) {
        showToast(context.l10n.modifySuccess);
        return imageFile;
      }
      showToast(context.l10n.modifyFail);
      return null;
    }
    return null;
  }
}
