import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/contact/widget/add_contact_menu_widget.dart';
import 'package:and/module/conversation/widget/ui_conversation_item.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:and/widget/connect_status_widget.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'home_conversation_logic.dart';

class HomeConversationPage extends StatefulWidget {
  const HomeConversationPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _HomeConversationPageState();
  }
}

class _HomeConversationPageState extends RefreshState<HomeConversationPage>
    with RouteAware {
  final logic = Get.put<HomeConversationLogic>(HomeConversationLogic());
  late final list = logic.list;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();

    Get.delete<HomeConversationLogic>();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (mounted) {
      logic.refreshData();
    }
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
          title: _buildTitle(),
          titleSpacing: 20,
          actions: [
            Padding(
                padding: EdgeInsets.only(right: 15),
                child: AddContactMenuWidget())
          ],
        ),
        body: Obx(() => _buildContent()));
  }

  Widget _buildTitle() {
    return Row(children: [
      Text(context.l10n.tabbarConversations),
      Obx(() => _buildConnectStatus()),
    ]);
  }

  Widget _buildContent() {
    return Column(
      children: [
        ConnectStatusWidget(logic.status.value),
        Expanded(child: _buildRefreshList())
      ],
    );
  }

  Widget _buildConnectStatus() {
    if (CacheHelper.devTestMode) {
      if (logic.status.value == WKConnectStatus.success ||
          logic.status.value == WKConnectStatus.syncCompleted ||
          logic.statusStr.isEmpty) {
        return Container();
      }
      return Text(" (${logic.statusStr.value})",
          style: TextStyles.fontSize13Normal
              .copyWith(color: DColor.secondaryTextColor));
    }
    if (logic.status.value == WKConnectStatus.connecting ||
        logic.status.value == WKConnectStatus.syncMsg) {
      return Container(
          margin: EdgeInsets.only(left: 8.0),
          width: 10.0,
          height: 10.0,
          child: CircularProgressIndicator(
            backgroundColor: Colors.transparent,
            strokeWidth: 1,
            valueColor: AlwaysStoppedAnimation<Color>(DColor.primaryColor),
          ));
    }
    return Container();
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      refreshController: refreshController,
      loadStatus: logic.loadStatus.value,
      hasData: logic.list.isNotEmpty,
      builder: (physics) => _buildList(physics),
      onRefresh: () {
        logic.refreshData();
      },
    );
  }

  Widget _buildList(ScrollPhysics? physics) {
    return NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification is ScrollStartNotification &&
              notification.dragDetails != null) {
            FocusScope.of(context).unfocus();
            SystemChannels.textInput.invokeMethod('TextInput.hide');
          }
          return true;
        },
        child: CustomScrollView(
          physics: physics,
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          slivers: [
            _buildSearchBar(),
            SliverPadding(
                padding: const EdgeInsets.symmetric(vertical: 15),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                      (BuildContext context, int index) {
                    WKUIConversationMsg item = list[index];
                    return UiConversationItem(
                      msg: item,
                      onTap: () {
                        ChatPage.open(
                            channelID: item.channelID,
                            channelType: item.channelType);
                      },
                      onLongPressTap: (details) async {
                        _onShowMenu(item, details);
                      },
                    );
                  }, childCount: list.length),
                ))
          ],
        ));
  }

  void _onShowMenu(
      WKUIConversationMsg item, LongPressStartDetails details) async {
    var channel = await item.getWkChannel();
    var isPin = channel?.top == 1;
    var isMute = channel?.mute == 1;
    if (channel == null) return;
    var items = [
      MenuItem(
          text: isMute
              ? context.l10n.openChannelNotice
              : context.l10n.closeChannelNotice,
          icon: isMute ? ImagePath.ic_msg_unmute : ImagePath.ic_msg_mute,
          onTap: () {
            logic.updateMute(channel.channelID, channel.channelType, !isMute);
          }),
      MenuItem(
          text: isPin ? context.l10n.msgCancelTop : context.l10n.msgSetTop,
          icon: isPin ? ImagePath.ic_msg_unpin : ImagePath.ic_msg_pin,
          onTap: () {
            logic.updatePin(channel.channelID, channel.channelType, !isPin);
          }),
      MenuItem(
          text: context.l10n.deleteChat,
          icon: ImagePath.ic_msg_delete,
          onTap: () {
            logic.deleteMsg(context, channel.channelID, channel.channelType);
          })
    ];
    await PopMenuUtil.showPopupMenu(context,
        Offset(details.globalPosition.dx, details.globalPosition.dy), items);
  }

  Widget _buildSearchBar() {
    return SliverToBoxAdapter(
      child: GestureDetector(
        onTap: () {
          GlobalSearchPage.open();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            height: 40,
            width: 300,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.search, color: Colors.grey),
                const SizedBox(width: 10),
                Text(
                  context.l10n.search,
                  style: TextStyle(color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
