import 'dart:io';
import 'dart:math';

import 'package:and/model/extension/wk_image_content_ext.dart';
import 'package:and/model/extension/wk_video_content_ext.dart';
import 'package:and/module/chat/widget/message/common/msg_image_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_video_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/module/video/video_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/image/simple_pics_wiper.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/model/wk_video_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'message_content_renderer.dart';

class VideoMessageRenderer extends MessageContentRenderer {
  final double maxHeight = 200;
  final double maxWidth = 150;

  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        VideoContentWidget(
            channelID: msg.channelID,
            channelType: msg.channelType,
            messageID: msg.messageID,
            videoContent: msg.messageContent as WKVideoContent,
            maxHeight: maxHeight, maxWidth: maxWidth),
        Container(
          padding: EdgeInsets.only(top: 4),
          child: MsgStatusWidget(
              msg: msg,
              onResendMsgTap: () {
                callback.resendMessage(msg);
              }),
        )
      ],
    );
  }

  @override
  Widget reply(BuildContext context, WKMsg msg) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    return FutureBuilder<WKMsg?>(
      future: WKIM.shared.messageManager.getWithMessageID(reply.messageId),
      builder: (BuildContext context, AsyncSnapshot<WKMsg?> snapshot) {
        var replyMsg = snapshot.data ?? msg;
        return VideoContentWidget(
          channelID: replyMsg.channelID,
          channelType: replyMsg.channelType,
          messageID: reply.messageId,
          videoContent: reply.payload as WKVideoContent,
          width: 40,
          height: 40,
          showDuration: false,
        );
      },
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.contentType == WkMessageContentType.video;
  }
}
