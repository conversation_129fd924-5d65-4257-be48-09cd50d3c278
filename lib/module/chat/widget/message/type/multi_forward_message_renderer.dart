import 'package:and/common/res/colours.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_multi_forward_content.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_multi_forward_content_ext.dart';
import 'package:and/module/chat/multiForward/multi_forward_page.dart';
import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'message_content_renderer.dart';

class MultiForwardMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    WKMultiForwardContent multiForwardContent =
        msg.messageContent! as WKMultiForwardContent;

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        MultiForwardPage.open(
            clientMsgNo: msg.clientMsgNO, content: multiForwardContent);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(multiForwardContent.getDisplayName(context),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.fontSize14Bold
                  .copyWith(color: DColor.primaryTextColor)),
          MultiForwardContentWidget(multiForwardContent: multiForwardContent),
          Divider(color: DColor.divider),
          Row(children: [
            Expanded(child: Text(context.l10n.chatRecord)),
            MsgStatusWidget(
                msg: msg,
                onResendMsgTap: () {
                  callback.resendMessage(msg);
                })
          ])
        ],
      ),
    );
  }

  @override
  Widget reply(BuildContext context, WKMsg msg) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    WKMultiForwardContent multiForwardContent =
        reply.payload! as WKMultiForwardContent;
    return InkWell(
      onTap: () {
        MultiForwardPage.open(content: multiForwardContent);
      },
      child: super.reply(context, msg),
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.contentType == WkMessageContentTypeExt.multiForward;
  }

  @override
  bool needContainer() {
    return true;
  }
}

class MultiForwardContentWidget extends StatefulWidget {
  final WKMultiForwardContent multiForwardContent;

  const MultiForwardContentWidget(
      {super.key, required this.multiForwardContent});

  @override
  State<StatefulWidget> createState() {
    return _MultiForwardContentWidgetState();
  }
}

class _MultiForwardContentWidgetState extends State<MultiForwardContentWidget> {
  late Future<String?> _contentFuture;

  @override
  void initState() {
    super.initState();
    _contentFuture = getContentDisplay();
  }

  @override
  void didUpdateWidget(covariant MultiForwardContentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _contentFuture = getContentDisplay();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      _buildContent(),
    ]);
  }

  Widget _buildContent() {
    return FutureBuilder<String?>(
      future: _contentFuture,
      builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
        return Text(snapshot.data ?? '',
            style: TextStyles.fontSize15Normal
                .copyWith(color: DColor.secondaryTextColor));
      },
    );
  }

  Future<String> getContentDisplay() async {
    var multiForwardContent = widget.multiForwardContent;
    if (multiForwardContent.msgList.isEmpty) {
      return '';
    }

    int size = multiForwardContent.msgList.length > 3
        ? 3
        : multiForwardContent.msgList.length;
    List<String> messages = [];

    for (int i = 0; i < size; i++) {
      WKMsg msg = multiForwardContent.msgList[i];
      String name = '';
      String content = '';
      var fromUID = msg.fromUID;
      var messageContent = msg.messageContent;

      // 获取消息内容
      messageContent ??=
          (await WKIM.shared.messageManager.getWithMessageID(msg.messageID))
              ?.messageContent;

      if (messageContent != null) {
        content = messageContent.displayText();
        // 如果文字太长滑动会卡顿
        if (content.length > 100) {
          content = content.substring(0, 80);
        }
      }
      if (content.isEmpty) {
        content = context.l10n.unknowMsgType;
      }

      // 获取通道名称
      if (fromUID.isNotEmpty) {
        var channel = await WKIM.shared.channelManager
            .getChannel(msg.fromUID, WKChannelType.personal);
        if (channel != null) {
          name = channel.displayName;
        } else {
          WKIM.shared.channelManager
              .fetchChannelInfo(msg.fromUID, WKChannelType.personal);
        }
      }

      // 构建消息内容
      if (name.isNotEmpty && content.isNotEmpty) {
        messages.add('$name: $content');
      }
    }
    return messages.join('\n');
  }
}
