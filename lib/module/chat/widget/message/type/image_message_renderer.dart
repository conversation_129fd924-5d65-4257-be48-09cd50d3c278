import 'dart:io';
import 'dart:math';

import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/model/extension/wk_image_content_ext.dart';
import 'package:and/module/chat/widget/message/common/msg_image_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/image/simple_pics_wiper.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'message_content_renderer.dart';

class ImageMessageRenderer extends MessageContentRenderer {
  final double maxHeight = 200;
  final double maxWidth = 150;

  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return Stack(
      children: [
        ImageContentWidget(
            maxHeight: maxHeight,
            maxWidth: maxWidth,
            imageContent: msg.messageContent as WKImageContent),
        Positioned(
            bottom: 5,
            right: 5,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(4),
              ),
              child: MsgStatusWidget(
                  msg: msg,
                  textColor: Colors.white,
                  onResendMsgTap: () {
                    callback.resendMessage(msg);
                  }),
            ))
      ],
    );
  }

  @override
  Widget reply(BuildContext context, WKMsg msg) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    return ImageContentWidget(
        height: 40, width: 40, imageContent: reply.payload as WKImageContent);
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.contentType == WkMessageContentType.image ||
        msg.contentType == WkMessageContentType.gif ||
        msg.contentType == WkMessageContentTypeExt.sticker;
  }
}
