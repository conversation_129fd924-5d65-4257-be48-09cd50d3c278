import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/file_dir_keys.dart';
import 'package:and/model/extension/wk_media_content_ext.dart';
import 'package:and/model/extension/wk_video_content_ext.dart';
import 'package:and/module/video/video_page.dart';
import 'package:and/utils/download/chat_file_utils.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/time_utils.dart';
import 'package:and/utils/video_utils.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/model/wk_video_content.dart';

class VideoContentWidget extends StatefulWidget {
  final String channelID;
  final int channelType;
  final String messageID;
  final WKVideoContent videoContent;
  final double maxHeight;
  final double maxWidth;
  final double? width;
  final double? height;
  final bool showDuration;

  const VideoContentWidget(
      {super.key,
      required this.channelID,
      required this.channelType,
      required this.messageID,
      required this.videoContent,
      this.width,
      this.height,
      this.maxHeight = 200,
      this.maxWidth = 150,
      this.showDuration = true});

  @override
  State<StatefulWidget> createState() {
    return _VideoContentWidgetState();
  }
}

class _VideoContentWidgetState extends State<VideoContentWidget> {
  WKVideoContent get videoContent => widget.videoContent;

  late StreamSubscription<DownloadStatus> _downloadSubscription;
  late DownloadManager downloadManager = DownloadManager();

  late VideoPlayUtils videoPlayUtils = VideoPlayUtils(
    videoContent,
    channelID: widget.channelID,
    channelType: widget.channelType,
    messageID: widget.messageID,
  );

  final _downloadStatus = Rx<DownloadStatus?>(null);

  @override
  void initState() {
    super.initState();

    _downloadStatus.value = downloadManager.statusForId(widget.messageID);
    _downloadSubscription = downloadManager.progressStream.listen((downStatus) {
      var isCurrentTask = downStatus.taskId == widget.messageID;
      if (!isCurrentTask) {
        return;
      }

      var isFinishDownload = (downStatus.progress ?? 0) >= 1;
      if (isFinishDownload) {
        _downloadStatus.value = null;
      } else {
        _downloadStatus.value = downStatus;
      }
    });

    _initCover();
  }

  _initCover() async {
    if (!mounted) return; // 防止组件已销毁时调用 setState

    var imageUri = videoContent.coverUri;
    if (!imageUri.hasEmptyPath) return; // 如果已有封面则直接返回

    String? coverLocalPath = videoContent.coverLocalPath;
    if (coverLocalPath.isEmpty) {
      try {
        coverLocalPath =
        await VideoUtils.getVideoThumbnail(videoContent.videoUrl);
        if (coverLocalPath != null && mounted) {
          videoContent.coverLocalPath = coverLocalPath;
          if (mounted) {
            setState(() {});
          }
        }
      } catch (e) {
        print("获取视频封面失败: $e");
      }
    }
  }

  @override
  void dispose() {
    _downloadSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double originalWidth = videoContent.width.toDouble();
    double originalHeight = videoContent.height.toDouble();
    if (originalHeight == 0) {
      originalHeight = widget.maxHeight;
    }
    if (originalWidth == 0) {
      originalWidth = widget.maxWidth;
    }
    double widthRatio = widget.maxWidth / originalWidth;
    double heightRatio = widget.maxHeight / originalHeight;
    double ratio = min(widthRatio, heightRatio);

    double imageWidth = originalWidth * ratio;
    double imageHeight = originalHeight * ratio;
    if (widget.width != null) {
      imageWidth = widget.width!;
    }
    if (widget.height != null) {
      imageHeight = widget.height!;
    }
    var imageUri = videoContent.coverUri;
    return GestureDetector(
      onTap: () {
        videoPlayUtils.viewOrDownload(onViewFile: () {
          videoPlayUtils.playVideo();
        });
      },
      child: Stack(
        children: [
          _buildImage(context, imageUri, imageWidth, imageHeight),
          Positioned(
              left: 5,
              top: 5,
              child: widget.showDuration
                  ? Container(
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        TimeUtils.formatDuration(widget.videoContent.second),
                        style: TextStyles.fontSize13Normal.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    )
                  : Container()),
          Positioned(
              left: (imageWidth - 26) / 2,
              top: (imageHeight - 26) / 2,
              child: _buildVideoIcon())
        ],
      ),
    );
  }

  Widget _buildVideoIcon() {
    return Container(
        width: 26,
        height: 26,
        decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.4), shape: BoxShape.circle),
        child: Stack(
          children: [
            Obx(() => _buildDownloadProgressView()),
            Center(
              child:
                  const Icon(Icons.play_arrow, size: 20, color: Colors.white),
            ),
          ],
        ));
  }

  Widget _buildDownloadProgressView() {
    var isDownload = _downloadStatus.value?.status == TaskStatus.enqueued ||
        _downloadStatus.value?.status == TaskStatus.running;
    if (!isDownload) {
      return Container();
    }
    return CircularProgressIndicator(
      value: ((_downloadStatus.value?.progress ?? 0) > 0)
          ? (_downloadStatus.value?.progress ?? 0)
          : null,
      backgroundColor: Colors.transparent,
      strokeWidth: 2,
      valueColor: AlwaysStoppedAnimation<Color>(DColor.primaryColor),
    );
  }

  Widget _buildImage(BuildContext context, Uri imageUri, double imageWidth,
      double imageHeight) {
    if (imageUri.isHttpUri) {
      return ExtendedImage.network(
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(8),
          clipBehavior: Clip.antiAlias,
          width: imageWidth,
          height: imageHeight,
          imageUri.toString(),
          fit: BoxFit.cover);
    }

    var file = File.fromUri(imageUri);
    if (!file.existsSync()) {
      return Container(
        width: imageWidth,
        height: imageHeight,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
      );
    }
    return ExtendedImage.file(file,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(8),
        clipBehavior: Clip.antiAlias,
        width: imageWidth,
        height: imageHeight,
        fit: BoxFit.cover);
  }
}

class VideoPlayUtils extends ChatFileUtils {
  final WKVideoContent videoContent;

  VideoPlayUtils(
    this.videoContent, {
    required super.messageID,
    required super.channelType,
    required super.channelID,
  }) : super(
          url: videoContent.videoUrl,
          fileSize: videoContent.size,
          folderName: FileDirKeys.wkVideos,
          localPath: videoContent.localPath,
        );

  void playVideo() {
    final file = File.fromUri(getFileUri());
    if (file.existsSync()) {
      Get.to(VideoPage(coverUri: videoContent.coverUri, uri: getFileUri()));
    }
  }
}
