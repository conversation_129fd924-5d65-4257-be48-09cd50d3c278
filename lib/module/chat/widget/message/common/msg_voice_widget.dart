import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/file_dir_keys.dart';
import 'package:and/model/extension/wk_voice_content_ext.dart';
import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/utils/audio_play_manager.dart';
import 'package:and/utils/download/chat_file_utils.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/time_utils.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';
import 'package:get/state_manager.dart';
import 'package:just_audio/just_audio.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_voice_content.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class VoiceContentWidget extends StatefulWidget {
  final String channelID;
  final int channelType;
  final String messageID;
  final WKVoiceContent voiceContent;
  final Widget? extraWidget;

  const VoiceContentWidget(
      {super.key,
      required this.channelID,
      required this.channelType,
      required this.messageID,
      required this.voiceContent,
      this.extraWidget});

  @override
  State<StatefulWidget> createState() {
    return _VoiceContentWidgetState();
  }
}

class _VoiceContentWidgetState extends State<VoiceContentWidget> {
  WKVoiceContent get voiceContent => widget.voiceContent;

  late StreamSubscription<double> _progressSubscription;
  late StreamSubscription<PlayerState> _playSubscription;
  late StreamSubscription<DownloadStatus> _downloadSubscription;

  late VoicePlayUtils voicePlayerUtils = VoicePlayUtils(
    voiceContent,
    channelID: widget.channelID,
    channelType: widget.channelType,
    messageID: widget.messageID,
  );
  late AudioPlayManager audioManager = AudioPlayManager();
  late DownloadManager downloadManager = DownloadManager();

  final _downloadStatus = Rx<DownloadStatus?>(null);
  final _playProgress = Rx<double>(0);

  @override
  void initState() {
    super.initState();

    _downloadStatus.value = downloadManager.statusForId(widget.messageID);

    _progressSubscription = audioManager.progressStream.listen((progress) {
      var isCurrentPlay = voicePlayerUtils.isCurrentPlayId;
      if (isCurrentPlay) {
        if (progress >= 1) {
          _playProgress.value = 0;
        } else {
          _playProgress.value = min(progress, 1);
        }
      } else {
        _playProgress.value = 0;
      }
    });
    _playSubscription = audioManager.playStream.listen((play) {
      setState(() {});
    });
    _downloadSubscription = downloadManager.progressStream.listen((downStatus) {
      var isCurrentTask = downStatus.taskId == widget.messageID;
      if (!isCurrentTask) {
        return;
      }

      var isFinishDownload = (downStatus.progress ?? 0) >= 1;
      if (isFinishDownload) {
        voicePlayerUtils.startPlay();
        _downloadStatus.value = null;
      } else {
        _downloadStatus.value = downStatus;
      }
    });
  }

  @override
  void dispose() {
    _progressSubscription.cancel();
    _playSubscription.cancel();
    _downloadSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildPlayButton(context, voiceContent),
        SizedBox(width: 10),
        Expanded(
            child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() => _buildPlayProgressView()),
            SizedBox(height: 6),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  TimeUtils.formatDuration(voiceContent.timeTrad,
                      defaultValue: "00:01"),
                  style: TextStyles.fontSize13Normal
                      .copyWith(color: DColor.secondaryTextColor),
                ),
                Spacer(),
                widget.extraWidget ?? Container(),
              ],
            )
          ],
        )),
      ],
    );
  }

  Widget _buildPlayButton(BuildContext context, WKVoiceContent voiceContent) {
    return GestureDetector(
      onTap: () => _handlePlayButtonTap(voiceContent),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: DColor.primaryColor.withOpacity(0.1),
        ),
        child: Stack(
          children: [
            Obx(() => _buildDownloadProgressView()),
            Center(
              child: Icon(
                voicePlayerUtils.isPlaying ? Icons.pause : Icons.play_arrow,
                color: DColor.primaryColor,
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadProgressView() {
    var isDownload = _downloadStatus.value?.status == TaskStatus.enqueued ||
        _downloadStatus.value?.status == TaskStatus.running;
    if (!isDownload) {
      return Container();
    }
    return CircularProgressIndicator(
      value: ((_downloadStatus.value?.progress ?? 0) > 0)
          ? (_downloadStatus.value?.progress ?? 0)
          : null,
      backgroundColor: Colors.transparent,
      valueColor: AlwaysStoppedAnimation<Color>(DColor.primaryColor),
    );
  }

  Widget _buildPlayProgressView() {
    return LinearProgressIndicator(
      value: _playProgress.value,
      backgroundColor: DColor.primaryColor.withOpacity(0.1),
      valueColor: AlwaysStoppedAnimation<Color>(DColor.primaryColor),
    );
  }

  void _handlePlayButtonTap(WKVoiceContent voiceContent) async {
    voicePlayerUtils.playOrDownload();
  }
}

class VoicePlayUtils extends ChatFileUtils {
  final WKVoiceContent voiceContent;

  VoicePlayUtils(
    this.voiceContent, {
    required super.messageID,
    required super.channelType,
    required super.channelID,
  }) : super(
          url: voiceContent.audioUrl,
          fileSize: 0,
          folderName: FileDirKeys.wkVideos,
          localPath: voiceContent.localPath,
        );

  final AudioPlayManager audioManager = AudioPlayManager();

  bool get isCurrentPlayId {
    return audioManager.currentPlayId == messageID;
  }

  bool get isPlaying => audioManager.isPlaying && isCurrentPlayId;

  bool get isPause => audioManager.isPause && isCurrentPlayId;

  Future<void> playOrDownload() async {
    if (isPlaying) {
      audioManager.pause();
      return;
    }
    audioManager.stop();
    viewOrDownload(onViewFile: () {
      // Start playing
      startPlay();
    });
  }

  void startPlay() async {
    File file = File.fromUri(getFileUri());
    if (!file.existsSync()) {
      return;
    }
    await audioManager.play(file.path, playId: messageID);
  }
}
