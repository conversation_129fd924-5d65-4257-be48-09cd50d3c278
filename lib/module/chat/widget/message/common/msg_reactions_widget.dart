import 'package:and/common/res/text_styles.dart';
import 'package:and/module/chat/widget/reaction_action_widget.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/image_path.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class MsgReactionsWidget extends StatelessWidget {
  final GlobalKey _key = GlobalKey();
  final WKMsg msg;
  final bool isAnonymous;
  final Function(GlobalKey)? onPickReactionTap;
  final Function(String)? onEmojiReactionTap;

  MsgReactionsWidget(
      {super.key,
      required this.msg,
      required this.onPickReactionTap,
      this.isAnonymous = false,
      this.onEmojiReactionTap});

  @override
  Widget build(BuildContext context) {
    var reactionList = msg.reactionList ?? [];
    if (reactionList.isEmpty) {
      return SizedBox();
    }

    // 按emoji分组
    Map<String, List<WKMsgReaction>> groupedReactions = {};
    for (var reaction in reactionList) {
      if (!groupedReactions.containsKey(reaction.emoji)) {
        groupedReactions[reaction.emoji] = [];
      }
      groupedReactions[reaction.emoji]!.add(reaction);
    }

    return Padding(
      key: _key,
      padding: EdgeInsets.only(top: 8),
      child: RichText(
          textScaler: MediaQuery.of(context).textScaler,
          text: TextSpan(children: [
            ...groupedReactions.entries.map((entry) => WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: _buildItem(context, entry.key, entry.value))),
            WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: InkWell(
                  onTap: () {
                    onPickReactionTap?.call(_key);
                  },
                  child: Padding(
                      padding: EdgeInsets.all(4),
                      child: Image.asset(
                        ImagePath.ic_add_emoji,
                        width: 25,
                        height: 25,
                      )),
                )),
          ])),
    );
  }

  Widget _buildItem(
      BuildContext context, String emoji, List<WKMsgReaction> reactions) {
    var emojiReaction = emojis.firstWhere((e) => e.emoji == emoji);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      margin: EdgeInsets.only(right: 2, top: 2),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: RichText(
        textScaler: MediaQuery.of(context).textScaler,
        text: TextSpan(
          children: [
            WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: InkWell(
                  onTap: () {
                    onEmojiReactionTap?.call(emoji);
                  },
                  child: Image.asset(
                    emojiReaction.icon,
                    width: 20,
                    height: 20,
                  ),
                )),
            if (isAnonymous)
              TextSpan(
                text: (reactions.length > 1) ? " (${reactions.length})" : " ",
                style: TextStyles.fontSize13Normal,
              ),
            if (!isAnonymous)
              WidgetSpan(
                  alignment: PlaceholderAlignment.middle,
                  child: SizedBox(width: 4)),
            if (!isAnonymous)
              ...reactions.asMap().entries.map((entry) {
                final reaction = entry.value;
                return TextSpan(
                  text: reaction.name +
                      (entry.key < reactions.length - 1 ? ', ' : ''),
                  style: TextStyles.fontSize13Normal,
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      UserInfoPage.open(channelID: reaction.uid);
                    },
                );
              })
          ],
        ),
      ),
    );
  }
}
