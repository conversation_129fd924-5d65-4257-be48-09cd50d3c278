import 'dart:async';
import 'dart:io';

import 'package:and/app.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/file_dir_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/extension/wk_file_content_ext.dart';
import 'package:and/module/file/file_viewer_page.dart';
import 'package:and/module/video/video_page.dart';
import 'package:and/utils/download/chat_file_utils.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/image/simple_pics_wiper.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:open_file/open_file.dart';

class FileContentWidget extends StatefulWidget {
  final String channelID;
  final int channelType;
  final String messageID;
  final WKFileContent fileContent;
  final double? titleFontSize;
  final double? sizeFontSize;
  final Widget? extraWidget;

  const FileContentWidget(
      {super.key,
      required this.channelID,
      required this.channelType,
      required this.messageID,
      required this.fileContent,
      this.titleFontSize,
      this.sizeFontSize,
      this.extraWidget});

  @override
  State<StatefulWidget> createState() {
    return _FileContentWidgetState();
  }
}

class _FileContentWidgetState extends State<FileContentWidget> {
  late WKFileContent fileContent = widget.fileContent;

  late StreamSubscription<DownloadStatus> _downloadSubscription;
  late DownloadManager downloadManager = DownloadManager();

  late OpenFileUtils openFileUtils = OpenFileUtils(
      channelID: widget.channelID,
      channelType: widget.channelType,
      messageID: widget.messageID,
      url: fileContent.fileUrl,
      fileSize: fileContent.size,
      localPath: fileContent.localPath);

  final _downloadStatus = Rx<DownloadStatus?>(null);

  @override
  void initState() {
    super.initState();

    _downloadStatus.value = downloadManager.statusForId(widget.messageID);
    _downloadSubscription = downloadManager.progressStream.listen((downStatus) {
      var isCurrentTask = downStatus.taskId == widget.messageID;
      if (!isCurrentTask) {
        return;
      }

      var isFinishDownload = (downStatus.progress ?? 0) >= 1;
      if (isFinishDownload) {
        _downloadStatus.value = null;
      } else {
        _downloadStatus.value = downStatus;
      }
    });
  }

  @override
  void dispose() {
    _downloadSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () async {
          bool openResult;
          if (openFileUtils.isFileExist()) {
            openResult = await openFileUtils.openFile();
            if (openResult) {
              return;
            }
          }
          Get.to(FileViewerPage.fromContent(
              channelID: widget.channelID,
              channelType: widget.channelType,
              messageID: widget.messageID,
              fileContent: fileContent));
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(flex: 1, child: _buildFileInfo()),
            _buildFileIcon(),
          ],
        ));
  }

  Widget _buildFileInfo() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
              flex: 1,
              child: Text(
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  fileContent.fileName,
                  style: TextStyles.fontSize15Normal.copyWith(
                    fontSize: widget.titleFontSize ?? 15,
                  ))),
          Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  FormatUtils.formatFileSize(fileContent.size),
                  style: TextStyles.fontSize13Normal.copyWith(
                      fontSize: widget.sizeFontSize ?? 12,
                      color: DColor.secondaryTextColor),
                ),
                const SizedBox(width: 10),
                widget.extraWidget ?? Container()
              ])
        ]);
  }

  Widget _buildFileIcon() {
    return SizedBox(
        width: 25,
        height: 25,
        child: Stack(
          children: [
            Obx(() => _buildDownloadProgressView()),
            Center(
              child: Padding(
                padding: EdgeInsets.all(2),
                child: Image.asset(ImagePath.ic_file),
              ),
            ),
          ],
        ));
  }

  Widget _buildDownloadProgressView() {
    var isDownload = _downloadStatus.value?.status == TaskStatus.enqueued ||
        _downloadStatus.value?.status == TaskStatus.running;
    if (!isDownload) {
      return Container();
    }
    return CircularProgressIndicator(
      value: ((_downloadStatus.value?.progress ?? 0) > 0)
          ? (_downloadStatus.value?.progress ?? 0)
          : null,
      backgroundColor: Colors.transparent,
      strokeWidth: 2,
      valueColor: AlwaysStoppedAnimation<Color>(DColor.primaryColor),
    );
  }
}

class OpenFileUtils extends ChatFileUtils {
  OpenFileUtils(
      {required super.messageID,
      required super.channelType,
      required super.channelID,
      required super.url,
      required super.fileSize,
      super.folderName = FileDirKeys.chatDownloadFile,
      super.localPath = ""});

  Future<bool> openFile({Function()? onFileOpened}) async {
    var uri = getFileUri();
    var file = File.fromUri(uri);
    if (!file.existsSync()) {
      showToast(globalContext!.l10n.noAppToOpen);
      return false;
    }

    final mimeType = lookupMimeType(file.path);
    if (mimeType?.startsWith("image") ?? false) {
      onFileOpened?.call();
      previewImage(globalContext!, file.path);
      return true;
    } else if (mimeType?.startsWith("video") ?? false) {
      onFileOpened?.call();
      Get.to(VideoPage(uri: Uri.file(file.path)));
      return true;
    }
    return false;
  }

  Future<OpenResult> openFileByPlatform() async {
    var uri = getFileUri();
    var file = File.fromUri(uri);
    final mimeType = lookupMimeType(file.path);
    var openResult = await OpenFile.open(file.path, type: mimeType);
    if (openResult.type == ResultType.noAppToOpen ||
        openResult.type == ResultType.permissionDenied) {
      showToast(globalContext!.l10n.noAppToOpen);
    }
    return openResult;
  }
}
