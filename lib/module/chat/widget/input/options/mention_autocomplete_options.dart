import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/module/group/widget/ui_channel_member_avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

class MentionAutocompleteOptions extends StatelessWidget {
  final String query;
  final List<WKChannelMember> users;
  final ValueSetter<WKChannelMember> onMentionUserTap;
  final bool enableMentionAll;

  const MentionAutocompleteOptions({
    super.key,
    required this.users,
    required this.query,
    required this.onMentionUserTap,
    required this.enableMentionAll,
  });

  @override
  Widget build(BuildContext context) {
    final filterUsers = users.where((it) {
      if (it.memberUID == CacheHelper.uid) {
        return false;
      }

      final normalizedQuery = query.toLowerCase();
      return it.displayName
          .toLowerCase()
          .contains(normalizedQuery.toLowerCase());
    }).toList();

    if (filterUsers.isEmpty) return const SizedBox.shrink();
    if (enableMentionAll) {
      var allMember = WKChannelMember();
      allMember.memberUID = "-1";
      allMember.memberName = context.l10n.mentionAll;
      filterUsers.insert(0, allMember);
    }

    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      clipBehavior: Clip.hardEdge,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LimitedBox(
            maxHeight: MediaQuery.of(context).size.height * 0.3,
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: filterUsers.length,
              separatorBuilder: (_, __) => const Divider(height: 0),
              itemBuilder: (context, i) {
                final user = filterUsers.elementAt(i);
                return ListTile(
                    contentPadding: EdgeInsets.zero,
                    dense: true,
                    onTap: () {
                      onMentionUserTap(user);
                    },
                    title: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        children: [
                          UiChannelMemberAvatarWidget(item: user, size: 30),
                          SizedBox(width: 10),
                          Text(
                            user.displayName,
                            style: TextStyles.fontSize13Normal,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ));
              },
            ),
          ),
        ],
      ),
    );
  }
}
