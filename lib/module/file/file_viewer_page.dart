import 'dart:async';
import 'dart:io';

import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/file_dir_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/extension/wk_file_content_ext.dart';
import 'package:and/module/chat/widget/message/common/msg_file_widget.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/submit_button.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'widget/file_menu_widget.dart';

class FileViewerPage extends StatefulWidget {
  final String channelID;
  final int channelType;
  final String messageID;
  final String url;
  final int fileSize;
  final String? localPath;
  final Widget? extraWidget;

  const FileViewerPage(
      {super.key,
      required this.channelID,
      required this.channelType,
      required this.messageID,
      required this.url,
      required this.fileSize,
      this.localPath,
      this.extraWidget});

  FileViewerPage.fromContent(
      {super.key,
      required this.channelID,
      required this.channelType,
      required this.messageID,
      required WKFileContent fileContent,
      this.extraWidget})
      : url = fileContent.fileUrl,
        fileSize = fileContent.size,
        localPath = fileContent.localPath;

  @override
  State<StatefulWidget> createState() {
    return _FileViewerPageState();
  }
}

class _FileViewerPageState extends State<FileViewerPage> {
  late StreamSubscription<DownloadStatus> _downloadSubscription;
  late DownloadManager downloadManager = DownloadManager();

  late OpenFileUtils openFileUtils = OpenFileUtils(
      channelID: widget.channelID,
      channelType: widget.channelType,
      messageID: widget.messageID,
      url: widget.url,
      fileSize: widget.fileSize,
      localPath: widget.localPath ?? '');

  final _downloadStatus = Rx<DownloadStatus?>(null);

  @override
  void initState() {
    super.initState();

    _downloadStatus.value = downloadManager.statusForId(widget.messageID);
    _downloadSubscription = downloadManager.progressStream.listen((downStatus) {
      var isCurrentTask = downStatus.taskId == widget.messageID;
      if (!isCurrentTask) {
        return;
      }

      var isFinishDownload = (downStatus.progress ?? 0) >= 1;
      if (isFinishDownload) {
        _downloadStatus.value = null;
        //打开文件
        openFileUtils.openFile(onFileOpened: () {
          Get.back();
        });
        setState(() {});
      } else {
        _downloadStatus.value = downStatus;
      }
    });

    initDownload();
  }

  void initDownload() async {
    var isFileExist = openFileUtils.isFileExist();
    if (!isFileExist) {
      openFileUtils.viewOrDownload(onViewFile: () {
        openFileUtils.openFileByPlatform();
      });
    }
  }

  @override
  void dispose() {
    _downloadSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          actions: [_buildAction()],
        ),
        body: SafeArea(
            child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Center(child: _buildContent()),
        )));
  }

  Widget _buildAction() {
    var uri = openFileUtils.getUri();
    if (uri.isHttpUri) {
      return Container();
    }
    if (uri.isFileUri && !File.fromUri(uri).existsSync()) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: FileMenuWidget(uri: uri),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        const SizedBox(height: 60),
        _buildFileInfo(),
        const SizedBox(height: 30),
        Obx(() => _buildDownloadProgressView()),
        Spacer(),
        Obx(() => _buildDownloadAction())
      ],
    );
  }

  Widget _buildFileInfo() {
    var uri = openFileUtils.getUri();
    String fileName = uri.pathSegments.last;
    if (uri.isFileUri) {
      fileName = File.fromUri(uri).name;
    }
    return Column(
      children: [
        Image.asset(ImagePath.ic_file, height: 60),
        const SizedBox(height: 10),
        Text(
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            fileName,
            style: TextStyles.fontSize18Medium),
        const SizedBox(height: 10),
        Visibility(
            visible: openFileUtils.fileSize > 0,
            child: Text(
              context.l10n
                  .fileSize(FormatUtils.formatFileSize(openFileUtils.fileSize)),
              style: TextStyles.fontSize15Normal
                  .copyWith(color: DColor.secondaryTextColor),
            )),
      ],
    );
  }

  Widget _buildDownloadProgressView() {
    var isDownload = _downloadStatus.value?.status == TaskStatus.enqueued ||
        _downloadStatus.value?.status == TaskStatus.running;
    if (!isDownload) {
      return Container();
    }
    var progress = _downloadStatus.value?.progress ?? 0;
    return Row(
      children: [
        Expanded(
            child: LinearProgressIndicator(
          value: (progress > 0) ? progress : null,
          backgroundColor: Colors.grey,
          valueColor: AlwaysStoppedAnimation<Color>(DColor.primaryColor),
        )),
        SizedBox(width: 10),
        Text(
          "${((progress * 100)).toStringAsFixed(2)}%",
          style: TextStyles.fontSize15Normal
              .copyWith(color: DColor.secondaryTextColor),
        ),
      ],
    );
  }

  Widget _buildDownloadAction() {
    var isDownload = _downloadStatus.value?.status == TaskStatus.enqueued ||
        _downloadStatus.value?.status == TaskStatus.running;

    var isFileExist = openFileUtils.isFileExist();
    if (isFileExist) {
      return _buildOpenFile();
    }

    if (isDownload) {
      return Container();
    }

    return SubmitButton(
        onPressed: () {
          openFileUtils.openFileByPlatform();
        },
        text: context.l10n.download);
  }

  Widget _buildOpenFile() {
    return SubmitButton(
        onPressed: () {
          openFileUtils.openFileByPlatform();
        },
        text: context.l10n.openFile);
  }
}
