allprojects {
    repositories {
        google()
        mavenCentral()
    }
    subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    if (namespace == null) {
                        namespace project.group
                    }
                    if (project.name == "flutter_mute") {
                        compileOptions {
                            sourceCompatibility JavaVersion.VERSION_21
                            targetCompatibility JavaVersion.VERSION_21
                        }
                    }
                }
            }
            if (project.plugins.hasPlugin("com.android.application") ||
                    project.plugins.hasPlugin("com.android.library")) {
                project.android {
                    compileSdkVersion 35
                    buildToolsVersion "35.0.0"
                }
            }
        }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

dependencies {
}