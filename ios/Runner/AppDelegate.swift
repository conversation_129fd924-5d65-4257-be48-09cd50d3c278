import Flutter
import UIKit
//import FirebaseMessaging
import UserNotifications

@main
@objc class AppDelegate: FlutterAppDelegate {
    var bgTask: UIBackgroundTaskIdentifier = .invalid
    var flutterChannel: FlutterMethodChannel?
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        GeneratedPluginRegistrant.register(with: self)
        
        let controller = window?.rootViewController as! FlutterViewController
        flutterChannel = FlutterMethodChannel(name: "background_task", binaryMessenger: controller.binaryMessenger)
        
        flutterChannel?.setMethodCallHandler { [weak self] (call, result) in
            if call.method == "startBackgroundTask" {
                self?.startBackgroundTask()
                result(nil)
            } else if call.method == "endBackgroundTask" {
                self?.endBackgroundTask()
                result(nil)
            }
        }
        
        registerForPushNotifications()
        UNUserNotificationCenter.current().delegate = self
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    func startBackgroundTask() {
        let application = UIApplication.shared
        
        bgTask = application.beginBackgroundTask {
            print("后台任务超时，调用 Flutter 断开 IM 连接")
            self.flutterChannel?.invokeMethod("disconnectIM", arguments: nil)
            application.endBackgroundTask(self.bgTask)
            self.bgTask = .invalid
        }
        
        if bgTask == .invalid {
            print("无法开启后台任务")
            return
        }
    }
    
    func endBackgroundTask() {
        let application = UIApplication.shared
        print("App 进入前台，结束后台任务")
        if bgTask != .invalid {
            application.endBackgroundTask(bgTask)
            bgTask = .invalid
        }
    }
    
    func registerForPushNotifications() {
        UNUserNotificationCenter.current().requestAuthorization(
            options: [.alert, .sound, .badge]) { granted, error in
                print("推送权限：\(granted)")
                
                // 仅当授权成功时注册远程推送
                guard granted else { return }
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            }
    }
    
    // 注册成功，获取 deviceToken
    override func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
//        Messaging.messaging().apnsToken = deviceToken
        let token = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
        print("APNs 设备 Token: \(token)")
        if token.count > 0 {
            self.flutterChannel?.invokeMethod("uploadDeviceToken", arguments: ["deviceToken": token])
        }
    }
    
    // 注册失败
    override func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        print("APNs 注册失败: \(error.localizedDescription)")
    }
    
    override func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void){
        let userInfo = response.notification.request.content.userInfo
        
        completionHandler()
    }
}

